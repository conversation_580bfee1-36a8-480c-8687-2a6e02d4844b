'use client'

import { useState } from 'react'
import Sidebar from '@/components/Sidebar'
import TabSystem from '@/components/TabSystem'
import Header from '@/components/Header'

export default function Home() {
  const [selectedSector, setSelectedSector] = useState('lending')
  const [selectedTab, setSelectedTab] = useState('real-usecase')

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <Sidebar 
        selectedSector={selectedSector} 
        onSectorChange={setSelectedSector}
      />
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header />
        
        {/* Tab System and Content */}
        <div className="flex-1 overflow-hidden">
          <TabSystem 
            selectedSector={selectedSector}
            selectedTab={selectedTab}
            onTabChange={setSelectedTab}
          />
        </div>
      </div>
    </div>
  )
}
