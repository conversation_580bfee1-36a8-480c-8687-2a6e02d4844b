'use client'

import { cn } from '@/lib/utils'
import LendingRealUsecase from './lending/RealUsecase'
import LendingGlobal from './lending/Global'
import LendingLLM from './lending/LLM'
import LendingIdeal from './lending/Ideal'
import ComingSoon from './ComingSoon'

interface TabSystemProps {
  selectedSector: string
  selectedTab: string
  onTabChange: (tab: string) => void
}

const tabs = [
  { id: 'real-usecase', name: 'Real-usecase', description: 'Live data analysis' },
  { id: 'global', name: 'Global', description: 'Worldwide metrics' },
  { id: 'llm', name: 'LLM', description: 'AI insights' },
  { id: 'ideal', name: 'Ideal usecases', description: 'Best practices' }
]

export default function TabSystem({ selectedSector, selectedTab, onTabChange }: TabSystemProps) {
  const renderContent = () => {
    if (selectedSector === 'lending') {
      switch (selectedTab) {
        case 'real-usecase':
          return <LendingRealUsecase />
        case 'global':
          return <LendingGlobal />
        case 'llm':
          return <LendingLLM />
        case 'ideal':
          return <LendingIdeal />
        default:
          return <LendingRealUsecase />
      }
    }
    
    // For other sectors, show coming soon
    return <ComingSoon sector={selectedSector} />
  }

  return (
    <div className="flex flex-col h-full">
      {/* Tab Navigation */}
      <div className="bg-surface border-b border-border-color px-6 py-4">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-text-primary capitalize">
              {selectedSector} Analytics
            </h2>
            <p className="text-sm text-text-secondary">
              Comprehensive analysis and insights for {selectedSector} use cases
            </p>
          </div>
        </div>
        
        <div className="flex space-x-1 bg-surface-light rounded-lg p-1">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={cn(
                "flex-1 px-4 py-3 rounded-md text-sm font-medium transition-all duration-200",
                "hover:bg-surface hover:text-text-primary",
                selectedTab === tab.id
                  ? "bg-primary text-white shadow-sm"
                  : "text-text-secondary"
              )}
            >
              <div className="text-center">
                <div className="font-medium">{tab.name}</div>
                <div className="text-xs opacity-75 mt-1">{tab.description}</div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-auto">
        {renderContent()}
      </div>
    </div>
  )
}
