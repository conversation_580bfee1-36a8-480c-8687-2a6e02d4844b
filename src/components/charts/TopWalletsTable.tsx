'use client'

import { AlertTriangle, TrendingDown, ExternalLink } from 'lucide-react'
import { topRiskyWallets } from '@/data/liquidationData'
import { cn } from '@/lib/utils'

export default function TopWalletsTable() {
  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'Critical':
        return 'text-danger bg-danger/10 border-danger/20'
      case 'Very High':
        return 'text-accent bg-accent/10 border-accent/20'
      case 'High':
        return 'text-chart-3 bg-chart-3/10 border-chart-3/20'
      default:
        return 'text-text-secondary bg-surface-light border-border-color'
    }
  }

  const getRiskIcon = (riskLevel: string) => {
    if (riskLevel === 'Critical') {
      return <AlertTriangle className="w-4 h-4" />
    }
    return <TrendingDown className="w-4 h-4" />
  }

  return (
    <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-2 sm:space-y-0">
        <div>
          <h3 className="text-base sm:text-lg font-semibold text-text-primary mb-2">
            Top Wallets by Reputation
          </h3>
          <p className="text-sm text-text-secondary">
            Highest risk wallets based on zScore analysis
          </p>
        </div>
        <button className="text-primary hover:text-primary-light text-sm font-medium flex items-center space-x-1 self-start sm:self-auto">
          <span>View All</span>
          <ExternalLink className="w-4 h-4" />
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full min-w-[800px]">
          <thead>
            <tr className="border-b border-border-color">
              <th className="text-left py-3 px-2 sm:px-4 text-xs sm:text-sm font-medium text-text-secondary">
                Wallet Address
              </th>
              <th className="text-left py-3 px-2 sm:px-4 text-xs sm:text-sm font-medium text-text-secondary">
                Chain
              </th>
              <th className="text-left py-3 px-2 sm:px-4 text-xs sm:text-sm font-medium text-text-secondary">
                Score
              </th>
              <th className="text-left py-3 px-2 sm:px-4 text-xs sm:text-sm font-medium text-text-secondary">
                Risk Level
              </th>
              <th className="text-left py-3 px-2 sm:px-4 text-xs sm:text-sm font-medium text-text-secondary">
                Activity
              </th>
              <th className="text-left py-3 px-2 sm:px-4 text-xs sm:text-sm font-medium text-text-secondary">
                Attestations
              </th>
            </tr>
          </thead>
          <tbody>
            {topRiskyWallets.map((wallet, index) => (
              <tr key={index} className="border-b border-border-color/50 hover:bg-surface-light/50 transition-colors">
                <td className="py-4 px-2 sm:px-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-primary to-primary-light rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-medium">
                        {index + 1}
                      </span>
                    </div>
                    <div>
                      <p className="text-xs sm:text-sm font-medium text-text-primary font-mono">
                        {wallet.address}
                      </p>
                      <p className="text-xs text-text-secondary">Verified</p>
                    </div>
                  </div>
                </td>
                <td className="py-4 px-2 sm:px-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center">
                      <div className="w-3 h-3 bg-primary rounded-full" />
                    </div>
                    <span className="text-xs sm:text-sm text-text-primary">{wallet.chain}</span>
                  </div>
                </td>
                <td className="py-4 px-2 sm:px-4">
                  <div className="text-center">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-danger to-accent rounded-full flex items-center justify-center mx-auto mb-1">
                      <span className="text-white text-xs sm:text-sm font-bold">
                        {Math.round(wallet.zscore)}
                      </span>
                    </div>
                    <div className="text-xs text-text-secondary">zScore</div>
                  </div>
                </td>
                <td className="py-4 px-2 sm:px-4">
                  <div className={cn(
                    'inline-flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-1 rounded-full border text-xs font-medium',
                    getRiskColor(wallet.riskLevel)
                  )}>
                    {getRiskIcon(wallet.riskLevel)}
                    <span className="hidden sm:inline">{wallet.riskLevel}</span>
                    <span className="sm:hidden">{wallet.riskLevel.split(' ')[0]}</span>
                  </div>
                </td>
                <td className="py-4 px-2 sm:px-4">
                  <div className="flex items-center space-x-1">
                    <div className="w-12 sm:w-16 h-2 bg-surface-light rounded-full overflow-hidden">
                      <div
                        className="h-full bg-gradient-to-r from-primary to-primary-light rounded-full"
                        style={{ width: `${[16, 2, 90][index] || 45}%` }}
                      />
                    </div>
                    <span className="text-xs text-text-secondary ml-1 sm:ml-2">
                      {[16, 2, 90][index] || 45}%
                    </span>
                  </div>
                </td>
                <td className="py-4 px-2 sm:px-4">
                  <div className="flex items-center space-x-1">
                    {[1, 2, 3].map((_, i) => (
                      <div
                        key={i}
                        className={cn(
                          'w-2 h-2 rounded-full',
                          i < 2 ? 'bg-secondary' : 'bg-surface-light'
                        )}
                      />
                    ))}
                    <span className="text-xs text-text-secondary ml-1 sm:ml-2">2/3</span>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
