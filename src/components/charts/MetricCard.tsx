'use client'

import { LucideIcon } from 'lucide-react'
import { cn } from '@/lib/utils'

interface MetricCardProps {
  title: string
  value: string | number
  change?: string
  changeType?: 'positive' | 'negative' | 'neutral'
  icon?: LucideIcon
  description?: string
  className?: string
}

export default function MetricCard({
  title,
  value,
  change,
  changeType = 'neutral',
  icon: Icon,
  description,
  className
}: MetricCardProps) {
  const getChangeColor = () => {
    switch (changeType) {
      case 'positive':
        return 'text-secondary'
      case 'negative':
        return 'text-danger'
      default:
        return 'text-text-secondary'
    }
  }

  const getChangeIcon = () => {
    if (!change) return null
    const isPositive = changeType === 'positive'
    return (
      <span className={cn('text-xs', getChangeColor())}>
        {isPositive ? '↗' : changeType === 'negative' ? '↘' : '→'} {change}
      </span>
    )
  }

  return (
    <div className={cn(
      'bg-surface border border-border-color rounded-xl p-4 sm:p-6 card-hover',
      className
    )}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            {Icon && (
              <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                <Icon className="w-4 h-4 text-primary" />
              </div>
            )}
            <h3 className="text-xs sm:text-sm font-medium text-text-secondary">{title}</h3>
          </div>

          <div className="space-y-1">
            <p className="text-xl sm:text-2xl font-bold text-text-primary">{value}</p>
            {change && (
              <div className="flex items-center space-x-2">
                {getChangeIcon()}
              </div>
            )}
            {description && (
              <p className="text-xs text-text-muted">{description}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
