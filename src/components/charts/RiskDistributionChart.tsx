'use client'

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON><PERSON><PERSON>, <PERSON>, Tooltip } from 'recharts'
import { riskCategories } from '@/data/liquidationData'

export default function RiskDistributionChart() {
  const data = riskCategories.map(category => ({
    name: category.name,
    value: category.percentage,
    color: category.color,
    range: category.range
  }))

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-surface border border-border-color rounded-lg p-3 shadow-lg">
          <p className="text-text-primary font-medium">{data.name}</p>
          <p className="text-text-secondary text-sm">Range: {data.range}</p>
          <p className="text-primary font-semibold">{data.value}% of liquidations</p>
        </div>
      )
    }
    return null
  }

  const CustomLegend = ({ payload }: any) => {
    return (
      <div className="flex flex-wrap justify-center gap-4 mt-4">
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center space-x-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-sm text-text-secondary">{entry.value}</span>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
      <div className="mb-4 sm:mb-6">
        <h3 className="text-base sm:text-lg font-semibold text-text-primary mb-2">
          Wallet Population Score Distribution
        </h3>
        <p className="text-sm text-text-secondary">
          Risk assessment based on zScore ranges showing liquidation probability
        </p>
      </div>

      <div className="h-64 sm:h-80">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              innerRadius={60}
              outerRadius={120}
              paddingAngle={2}
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend content={<CustomLegend />} />
          </PieChart>
        </ResponsiveContainer>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-6">
        {riskCategories.map((category, index) => (
          <div key={index} className="bg-surface-light rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: category.color }}
              />
              <div>
                <p className="text-sm font-medium text-text-primary">{category.name}</p>
                <p className="text-xs text-text-secondary">{category.range}</p>
              </div>
            </div>
            <p className="text-lg font-bold text-text-primary mt-2">
              {category.percentage}%
            </p>
          </div>
        ))}
      </div>
    </div>
  )
}
