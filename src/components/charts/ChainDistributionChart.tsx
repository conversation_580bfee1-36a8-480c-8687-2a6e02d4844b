'use client'

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>ltip, ResponsiveContainer } from 'recharts'
import { chainDistribution } from '@/data/liquidationData'

export default function ChainDistributionChart() {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-surface border border-border-color rounded-lg p-3 shadow-lg">
          <p className="text-text-primary font-medium">{label}</p>
          <p className="text-primary font-semibold">{data.value.toLocaleString()} liquidations</p>
          <p className="text-text-secondary text-sm">{data.percentage}% of total</p>
        </div>
      )
    }
    return null
  }

  return (
    <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
      <div className="mb-4 sm:mb-6">
        <h3 className="text-base sm:text-lg font-semibold text-text-primary mb-2">
          Chains Distribution
        </h3>
        <p className="text-sm text-text-secondary">
          Liquidation events across different blockchain networks
        </p>
      </div>

      <div className="h-64 sm:h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={chainDistribution} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#334155" />
            <XAxis
              dataKey="name"
              stroke="#94A3B8"
              fontSize={12}
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis stroke="#94A3B8" fontSize={12} />
            <Tooltip content={<CustomTooltip />} />
            <Bar
              dataKey="value"
              fill="#6366F1"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-6">
        {chainDistribution.slice(0, 3).map((chain, index) => (
          <div key={index} className="bg-surface-light rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: chain.color }}
              />
              <div>
                <p className="text-sm font-medium text-text-primary">{chain.name}</p>
                <p className="text-xs text-text-secondary">{chain.percentage}%</p>
              </div>
            </div>
            <p className="text-lg font-bold text-text-primary mt-2">
              {chain.value.toLocaleString()}
            </p>
          </div>
        ))}
      </div>
    </div>
  )
}
