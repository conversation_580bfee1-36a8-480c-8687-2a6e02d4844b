'use client'

import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from 'recharts'
import { liquidationTrends } from '@/data/liquidationData'

export default function LiquidationTrendsChart() {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-surface border border-border-color rounded-lg p-3 shadow-lg">
          <p className="text-text-primary font-medium">{label}</p>
          <p className="text-primary font-semibold">
            {payload[0].value.toLocaleString()} liquidations
          </p>
          <p className="text-secondary font-semibold">
            Avg zScore: {payload[1].value}
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
      <div className="mb-4 sm:mb-6">
        <h3 className="text-base sm:text-lg font-semibold text-text-primary mb-2">
          Transaction Trends
        </h3>
        <p className="text-sm text-text-secondary">
          Daily liquidation events and average zScore over time (48-hour period)
        </p>
      </div>

      <div className="h-64 sm:h-80">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={liquidationTrends} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <defs>
              <linearGradient id="liquidationGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#6366F1" stopOpacity={0.3}/>
                <stop offset="95%" stopColor="#6366F1" stopOpacity={0}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#334155" />
            <XAxis
              dataKey="date"
              stroke="#94A3B8"
              fontSize={12}
              tickFormatter={(value) => new Date(value).toLocaleDateString()}
            />
            <YAxis stroke="#94A3B8" fontSize={12} />
            <Tooltip content={<CustomTooltip />} />
            <Area
              type="monotone"
              dataKey="liquidations"
              stroke="#6366F1"
              strokeWidth={2}
              fill="url(#liquidationGradient)"
            />
            <Line
              type="monotone"
              dataKey="avgZScore"
              stroke="#10B981"
              strokeWidth={2}
              dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-6">
        <div className="bg-surface-light rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-4 h-4 rounded-full bg-primary" />
            <div>
              <p className="text-sm font-medium text-text-primary">Peak Liquidations</p>
              <p className="text-xs text-text-secondary">Feb 2, 2025</p>
            </div>
          </div>
          <p className="text-lg font-bold text-text-primary mt-2">2,650</p>
        </div>

        <div className="bg-surface-light rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-4 h-4 rounded-full bg-secondary" />
            <div>
              <p className="text-sm font-medium text-text-primary">Avg zScore</p>
              <p className="text-xs text-text-secondary">48-hour period</p>
            </div>
          </div>
          <p className="text-lg font-bold text-text-primary mt-2">246</p>
        </div>
      </div>
    </div>
  )
}
