'use client'

import { 
  CreditCard, 
  Gift, 
  TrendingUp, 
  Zap,
  ChevronRight
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface SidebarProps {
  selectedSector: string
  onSectorChange: (sector: string) => void
}

const sectors = [
  {
    id: 'lending',
    name: 'Lending',
    icon: CreditCard,
    description: 'Credit risk assessment',
    color: 'text-chart-1',
    bgColor: 'bg-chart-1/10',
    borderColor: 'border-chart-1/20'
  },
  {
    id: 'airdrops',
    name: 'Airdrops',
    icon: Gift,
    description: 'Sybil resistance',
    color: 'text-chart-2',
    bgColor: 'bg-chart-2/10',
    borderColor: 'border-chart-2/20'
  },
  {
    id: 'dexes',
    name: 'DEXes',
    icon: TrendingUp,
    description: 'Trading reputation',
    color: 'text-chart-3',
    bgColor: 'bg-chart-3/10',
    borderColor: 'border-chart-3/20'
  },
  {
    id: 'perps',
    name: 'Perps',
    icon: Zap,
    description: 'Leverage assessment',
    color: 'text-chart-4',
    bgColor: 'bg-chart-4/10',
    borderColor: 'border-chart-4/20'
  }
]

export default function Sidebar({ selectedSector, onSectorChange }: SidebarProps) {
  return (
    <div className="w-80 bg-surface border-r border-border-color flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-border-color">
        <h2 className="text-lg font-semibold text-text-primary mb-2">Use Case Sectors</h2>
        <p className="text-sm text-text-secondary">
          Explore zScore applications across different DeFi sectors
        </p>
      </div>

      {/* Sectors List */}
      <div className="flex-1 p-4 space-y-3">
        {sectors.map((sector) => {
          const Icon = sector.icon
          const isSelected = selectedSector === sector.id
          
          return (
            <button
              key={sector.id}
              onClick={() => onSectorChange(sector.id)}
              className={cn(
                "w-full p-4 rounded-xl border transition-all duration-200 group",
                "hover:shadow-card-hover hover:scale-[1.02]",
                isSelected 
                  ? `${sector.bgColor} ${sector.borderColor} border-2 shadow-card` 
                  : "bg-surface-light border-border-color hover:border-primary/30"
              )}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={cn(
                    "w-10 h-10 rounded-lg flex items-center justify-center",
                    isSelected ? sector.bgColor : "bg-surface"
                  )}>
                    <Icon className={cn(
                      "w-5 h-5",
                      isSelected ? sector.color : "text-text-secondary"
                    )} />
                  </div>
                  <div className="text-left">
                    <h3 className={cn(
                      "font-medium",
                      isSelected ? "text-text-primary" : "text-text-primary group-hover:text-text-primary"
                    )}>
                      {sector.name}
                    </h3>
                    <p className={cn(
                      "text-xs",
                      isSelected ? "text-text-secondary" : "text-text-muted group-hover:text-text-secondary"
                    )}>
                      {sector.description}
                    </p>
                  </div>
                </div>
                <ChevronRight className={cn(
                  "w-4 h-4 transition-transform",
                  isSelected ? `${sector.color} rotate-90` : "text-text-muted group-hover:text-text-secondary"
                )} />
              </div>
            </button>
          )
        })}
      </div>

      {/* Footer */}
      <div className="p-6 border-t border-border-color">
        <div className="bg-gradient-to-r from-primary/10 to-primary-light/10 border border-primary/20 rounded-lg p-4">
          <h4 className="font-medium text-text-primary mb-1">Need Help?</h4>
          <p className="text-xs text-text-secondary mb-3">
            Learn more about zScore implementation
          </p>
          <button className="w-full bg-primary text-white text-sm py-2 rounded-lg hover:bg-primary-light transition-colors">
            View Documentation
          </button>
        </div>
      </div>
    </div>
  )
}
