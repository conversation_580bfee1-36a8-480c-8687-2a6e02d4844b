'use client'

import { Construction, Sparkles } from 'lucide-react'

interface ComingSoonProps {
  sector: string
}

export default function ComingSoon({ sector }: ComingSoonProps) {
  return (
    <div className="flex items-center justify-center h-full bg-background">
      <div className="text-center max-w-md mx-auto p-8">
        <div className="w-20 h-20 bg-gradient-to-r from-primary to-primary-light rounded-full flex items-center justify-center mx-auto mb-6">
          <Construction className="w-10 h-10 text-white" />
        </div>
        
        <h2 className="text-2xl font-bold text-text-primary mb-4 capitalize">
          {sector} Analytics Coming Soon
        </h2>
        
        <p className="text-text-secondary mb-6 leading-relaxed">
          We're working hard to bring you comprehensive analytics for {sector} use cases. 
          This section will include detailed metrics, risk assessments, and insights 
          specific to the {sector} sector.
        </p>
        
        <div className="bg-surface border border-border-color rounded-lg p-6 mb-6">
          <div className="flex items-center justify-center mb-4">
            <Sparkles className="w-6 h-6 text-accent mr-2" />
            <span className="text-accent font-medium">What's Coming</span>
          </div>
          <ul className="text-sm text-text-secondary space-y-2 text-left">
            <li>• Real-time {sector} metrics and KPIs</li>
            <li>• Risk assessment models</li>
            <li>• Historical trend analysis</li>
            <li>• Predictive insights using AI</li>
            <li>• Best practice recommendations</li>
          </ul>
        </div>
        
        <button className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-light transition-colors">
          Get Notified When Ready
        </button>
      </div>
    </div>
  )
}
