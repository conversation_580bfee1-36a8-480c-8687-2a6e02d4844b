'use client'

import { Globe, TrendingUp, Users, DollarSign, BarChart3, Activity } from 'lucide-react'
import MetricCard from '@/components/charts/MetricCard'

export default function LendingGlobal() {
  const globalMetrics = [
    { region: 'North America', volume: '$2.4B', growth: '+15.2%', users: '145K' },
    { region: 'Europe', volume: '$1.8B', growth: '+12.8%', users: '98K' },
    { region: 'Asia Pacific', volume: '$3.1B', growth: '+28.4%', users: '234K' },
    { region: 'Latin America', volume: '$0.6B', growth: '+45.1%', users: '67K' },
  ]

  const protocolMetrics = [
    { protocol: 'Aave', tvl: '$8.2B', users: '156K', chains: 8 },
    { protocol: 'Compound', tvl: '$3.4B', users: '89K', chains: 3 },
    { protocol: 'MakerDAO', tvl: '$5.1B', users: '67K', chains: 1 },
    { protocol: 'Venus', tvl: '$1.2B', users: '34K', chains: 2 },
  ]

  return (
    <div className="p-4 sm:p-6 space-y-4 sm:space-y-6 bg-background min-h-full">
      {/* Header */}
      <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex-1">
            <h1 className="text-xl sm:text-2xl font-bold text-text-primary mb-2">
              Global DeFi Lending Landscape
            </h1>
            <p className="text-text-secondary mb-4 text-sm sm:text-base">
              Worldwide analysis of DeFi lending protocols, regional adoption patterns,
              and global zScore implementation across different markets and regulatory environments.
            </p>
          </div>
          <div className="bg-gradient-to-r from-secondary/10 to-accent/10 border border-secondary/20 rounded-lg p-4 lg:ml-4 flex-shrink-0">
            <Globe className="w-8 h-8 text-secondary mx-auto mb-2" />
            <p className="text-sm font-medium text-text-primary text-center">Global Reach</p>
            <p className="text-2xl font-bold text-secondary text-center">180+</p>
            <p className="text-xs text-text-secondary text-center">Countries</p>
          </div>
        </div>
      </div>

      {/* Global Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <MetricCard
          title="Total Value Locked"
          value="$18.7B"
          change="+18.5% this quarter"
          changeType="positive"
          icon={DollarSign}
          description="Across all protocols"
        />
        <MetricCard
          title="Active Users"
          value="544K"
          change="+23.1% growth"
          changeType="positive"
          icon={Users}
          description="Monthly active borrowers"
        />
        <MetricCard
          title="Supported Chains"
          value="25"
          change="+5 new chains"
          changeType="positive"
          icon={BarChart3}
          description="Multi-chain deployment"
        />
        <MetricCard
          title="Daily Volume"
          value="$2.1B"
          change="+12.3% increase"
          changeType="positive"
          icon={Activity}
          description="24h lending volume"
        />
      </div>

      {/* Regional Analysis */}
      <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-6">Regional Market Analysis</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          {globalMetrics.map((region, index) => (
            <div key={index} className="bg-surface-light rounded-lg p-4">
              <h3 className="font-medium text-text-primary mb-3">{region.region}</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-text-secondary">Volume</span>
                  <span className="text-sm font-medium text-text-primary">{region.volume}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-secondary">Growth</span>
                  <span className="text-sm font-medium text-secondary">{region.growth}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-secondary">Users</span>
                  <span className="text-sm font-medium text-text-primary">{region.users}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Protocol Comparison */}
      <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-6">Major Protocol Metrics</h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border-color">
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Protocol</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">TVL</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Active Users</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Chains</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">zScore Integration</th>
              </tr>
            </thead>
            <tbody>
              {protocolMetrics.map((protocol, index) => (
                <tr key={index} className="border-b border-border-color/50 hover:bg-surface-light/50 transition-colors">
                  <td className="py-4 px-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
                        <span className="text-primary text-xs font-bold">
                          {protocol.protocol.charAt(0)}
                        </span>
                      </div>
                      <span className="font-medium text-text-primary">{protocol.protocol}</span>
                    </div>
                  </td>
                  <td className="py-4 px-4 text-text-primary font-medium">{protocol.tvl}</td>
                  <td className="py-4 px-4 text-text-primary">{protocol.users}</td>
                  <td className="py-4 px-4 text-text-primary">{protocol.chains}</td>
                  <td className="py-4 px-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      index === 0 ? 'bg-secondary/10 text-secondary border border-secondary/20' :
                      index === 1 ? 'bg-accent/10 text-accent border border-accent/20' :
                      'bg-surface-light text-text-secondary border border-border-color'
                    }`}>
                      {index === 0 ? 'Live' : index === 1 ? 'Beta' : 'Planned'}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Global Trends */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-surface border border-border-color rounded-xl p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Adoption Trends</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-text-secondary">Institutional Adoption</span>
              <div className="flex items-center space-x-2">
                <div className="w-24 h-2 bg-surface-light rounded-full">
                  <div className="w-16 h-2 bg-primary rounded-full"></div>
                </div>
                <span className="text-sm text-text-primary">67%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-text-secondary">Retail Participation</span>
              <div className="flex items-center space-x-2">
                <div className="w-24 h-2 bg-surface-light rounded-full">
                  <div className="w-20 h-2 bg-secondary rounded-full"></div>
                </div>
                <span className="text-sm text-text-primary">83%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-text-secondary">Cross-chain Usage</span>
              <div className="flex items-center space-x-2">
                <div className="w-24 h-2 bg-surface-light rounded-full">
                  <div className="w-12 h-2 bg-accent rounded-full"></div>
                </div>
                <span className="text-sm text-text-primary">45%</span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-surface border border-border-color rounded-xl p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Regulatory Landscape</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-surface-light rounded-lg">
              <div>
                <p className="text-sm font-medium text-text-primary">EU MiCA Compliance</p>
                <p className="text-xs text-text-secondary">Markets in Crypto-Assets</p>
              </div>
              <span className="text-xs bg-secondary/10 text-secondary px-2 py-1 rounded border border-secondary/20">
                Compliant
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-surface-light rounded-lg">
              <div>
                <p className="text-sm font-medium text-text-primary">US Regulatory Clarity</p>
                <p className="text-xs text-text-secondary">State-by-state approach</p>
              </div>
              <span className="text-xs bg-accent/10 text-accent px-2 py-1 rounded border border-accent/20">
                Pending
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-surface-light rounded-lg">
              <div>
                <p className="text-sm font-medium text-text-primary">APAC Framework</p>
                <p className="text-xs text-text-secondary">Singapore, Japan leading</p>
              </div>
              <span className="text-xs bg-secondary/10 text-secondary px-2 py-1 rounded border border-secondary/20">
                Active
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
