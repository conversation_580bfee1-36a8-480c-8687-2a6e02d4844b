'use client'

import { Brain, Zap, Target, TrendingUp, AlertCircle, CheckCircle } from 'lucide-react'
import MetricCard from '@/components/charts/MetricCard'

export default function LendingLLM() {
  const aiInsights = [
    {
      title: "Risk Pattern Detection",
      confidence: 94,
      insight: "AI identified 3 new risk patterns in wallet behavior that correlate with liquidation events within 24-48 hours.",
      impact: "High",
      status: "active"
    },
    {
      title: "Predictive Modeling",
      confidence: 87,
      insight: "Machine learning models predict liquidation probability with 87% accuracy using zScore and on-chain behavior.",
      impact: "Critical",
      status: "active"
    },
    {
      title: "Anomaly Detection",
      confidence: 91,
      insight: "Detected unusual trading patterns in 156 wallets that may indicate coordinated liquidation attacks.",
      impact: "Medium",
      status: "monitoring"
    }
  ]

  const modelPerformance = [
    { model: "zScore Predictor", accuracy: "87.3%", precision: "84.1%", recall: "89.7%" },
    { model: "Risk Classifier", accuracy: "92.1%", precision: "88.9%", recall: "91.4%" },
    { model: "Anomaly Detector", accuracy: "89.6%", precision: "86.2%", recall: "87.8%" },
    { model: "Liquidation Forecaster", accuracy: "85.7%", precision: "82.3%", recall: "88.1%" }
  ]

  return (
    <div className="p-4 sm:p-6 space-y-4 sm:space-y-6 bg-background min-h-full">
      {/* Header */}
      <div className="bg-surface border border-border-color rounded-xl p-6">
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-2xl font-bold text-text-primary mb-2">
              AI-Powered Lending Intelligence
            </h1>
            <p className="text-text-secondary mb-4 max-w-3xl">
              Advanced machine learning models and AI insights for enhanced risk assessment,
              predictive analytics, and automated decision-making in DeFi lending protocols.
            </p>
          </div>
          <div className="bg-gradient-to-r from-accent/10 to-primary/10 border border-accent/20 rounded-lg p-4">
            <Brain className="w-8 h-8 text-accent mx-auto mb-2" />
            <p className="text-sm font-medium text-text-primary text-center">AI Accuracy</p>
            <p className="text-2xl font-bold text-accent text-center">89.2%</p>
            <p className="text-xs text-text-secondary text-center">Average model performance</p>
          </div>
        </div>
      </div>

      {/* AI Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Models Deployed"
          value="12"
          change="+3 this month"
          changeType="positive"
          icon={Brain}
          description="Active AI models"
        />
        <MetricCard
          title="Predictions Made"
          value="2.4M"
          change="+15.7% accuracy"
          changeType="positive"
          icon={Target}
          description="Daily predictions"
        />
        <MetricCard
          title="Risk Alerts"
          value="1,247"
          change="89% accuracy rate"
          changeType="positive"
          icon={AlertCircle}
          description="Real-time alerts"
        />
        <MetricCard
          title="Processing Speed"
          value="<100ms"
          change="99.9% uptime"
          changeType="positive"
          icon={Zap}
          description="Average response time"
        />
      </div>

      {/* AI Insights */}
      <div className="bg-surface border border-border-color rounded-xl p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-6">Real-time AI Insights</h2>
        <div className="space-y-4">
          {aiInsights.map((insight, index) => (
            <div key={index} className="bg-surface-light border border-border-color rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    insight.status === 'active' ? 'bg-secondary' : 'bg-accent'
                  }`} />
                  <h3 className="font-medium text-text-primary">{insight.title}</h3>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`text-xs px-2 py-1 rounded border ${
                    insight.impact === 'Critical' ? 'bg-danger/10 text-danger border-danger/20' :
                    insight.impact === 'High' ? 'bg-accent/10 text-accent border-accent/20' :
                    'bg-primary/10 text-primary border-primary/20'
                  }`}>
                    {insight.impact} Impact
                  </span>
                  <span className="text-xs text-text-secondary">
                    {insight.confidence}% confidence
                  </span>
                </div>
              </div>
              <p className="text-sm text-text-secondary leading-relaxed">{insight.insight}</p>
              <div className="mt-3 flex items-center justify-between">
                <div className="w-full bg-surface rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-primary to-primary-light h-2 rounded-full"
                    style={{ width: `${insight.confidence}%` }}
                  />
                </div>
                <span className="ml-3 text-xs text-text-secondary">{insight.confidence}%</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Model Performance */}
      <div className="bg-surface border border-border-color rounded-xl p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-6">Model Performance Metrics</h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border-color">
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Model</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Accuracy</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Precision</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Recall</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Status</th>
              </tr>
            </thead>
            <tbody>
              {modelPerformance.map((model, index) => (
                <tr key={index} className="border-b border-border-color/50 hover:bg-surface-light/50 transition-colors">
                  <td className="py-4 px-4">
                    <div className="flex items-center space-x-3">
                      <Brain className="w-5 h-5 text-primary" />
                      <span className="font-medium text-text-primary">{model.model}</span>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className="flex items-center space-x-2">
                      <span className="text-text-primary font-medium">{model.accuracy}</span>
                      <div className="w-16 h-2 bg-surface-light rounded-full">
                        <div
                          className="h-2 bg-secondary rounded-full"
                          style={{ width: model.accuracy }}
                        />
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4 text-text-primary">{model.precision}</td>
                  <td className="py-4 px-4 text-text-primary">{model.recall}</td>
                  <td className="py-4 px-4">
                    <span className="inline-flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium bg-secondary/10 text-secondary border border-secondary/20">
                      <CheckCircle className="w-3 h-3" />
                      <span>Active</span>
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* AI Recommendations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-surface border border-border-color rounded-xl p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Automated Recommendations</h3>
          <div className="space-y-3">
            <div className="bg-surface-light rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <TrendingUp className="w-5 h-5 text-secondary mt-1" />
                <div>
                  <p className="text-sm font-medium text-text-primary">Optimize Collateral Ratios</p>
                  <p className="text-xs text-text-secondary mt-1">
                    AI suggests increasing collateral requirements for wallets with zScore &lt; 150
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-surface-light rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-accent mt-1" />
                <div>
                  <p className="text-sm font-medium text-text-primary">Enhanced Monitoring</p>
                  <p className="text-xs text-text-secondary mt-1">
                    Deploy additional monitoring for 234 wallets showing anomalous patterns
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-surface-light rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Target className="w-5 h-5 text-primary mt-1" />
                <div>
                  <p className="text-sm font-medium text-text-primary">Dynamic Pricing</p>
                  <p className="text-xs text-text-secondary mt-1">
                    Implement risk-based interest rates using real-time zScore updates
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-surface border border-border-color rounded-xl p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Training Data Quality</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-text-secondary">Data Completeness</span>
              <div className="flex items-center space-x-2">
                <div className="w-24 h-2 bg-surface-light rounded-full">
                  <div className="w-22 h-2 bg-secondary rounded-full"></div>
                </div>
                <span className="text-sm text-text-primary">94%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-text-secondary">Data Freshness</span>
              <div className="flex items-center space-x-2">
                <div className="w-24 h-2 bg-surface-light rounded-full">
                  <div className="w-20 h-2 bg-primary rounded-full"></div>
                </div>
                <span className="text-sm text-text-primary">87%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-text-secondary">Label Accuracy</span>
              <div className="flex items-center space-x-2">
                <div className="w-24 h-2 bg-surface-light rounded-full">
                  <div className="w-23 h-2 bg-accent rounded-full"></div>
                </div>
                <span className="text-sm text-text-primary">96%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-text-secondary">Feature Diversity</span>
              <div className="flex items-center space-x-2">
                <div className="w-24 h-2 bg-surface-light rounded-full">
                  <div className="w-21 h-2 bg-chart-4 rounded-full"></div>
                </div>
                <span className="text-sm text-text-primary">91%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
