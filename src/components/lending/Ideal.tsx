'use client'

import {
  CheckCircle,
  Shield,
  Target,
  Zap,
  Users,
  TrendingUp,
  BookOpen,
  Award,
  Lightbulb
} from 'lucide-react'

export default function LendingIdeal() {
  const bestPractices = [
    {
      category: "Risk Assessment",
      icon: Shield,
      practices: [
        "Implement multi-layered zScore validation with real-time updates",
        "Use dynamic collateral ratios based on wallet reputation scores",
        "Deploy automated liquidation protection for high-risk positions",
        "Integrate cross-chain reputation data for comprehensive assessment"
      ]
    },
    {
      category: "User Experience",
      icon: Users,
      practices: [
        "Provide transparent zScore explanations to users",
        "Offer reputation improvement guidance and recommendations",
        "Implement gradual access to higher lending limits",
        "Create educational content about reputation building"
      ]
    },
    {
      category: "Protocol Design",
      icon: Target,
      practices: [
        "Design incentive mechanisms for positive reputation building",
        "Implement reputation-based fee structures",
        "Create governance participation rewards for high-reputation users",
        "Build reputation recovery mechanisms for reformed users"
      ]
    }
  ]

  const implementationSteps = [
    {
      phase: "Phase 1: Foundation",
      duration: "2-3 months",
      tasks: [
        "Deploy basic zScore integration",
        "Implement risk categorization",
        "Set up monitoring infrastructure",
        "Create user dashboard"
      ],
      status: "completed"
    },
    {
      phase: "Phase 2: Enhancement",
      duration: "3-4 months",
      tasks: [
        "Add cross-chain reputation tracking",
        "Implement dynamic pricing",
        "Deploy AI-powered risk models",
        "Create reputation recovery system"
      ],
      status: "in-progress"
    },
    {
      phase: "Phase 3: Optimization",
      duration: "2-3 months",
      tasks: [
        "Fine-tune risk parameters",
        "Implement advanced analytics",
        "Add governance integration",
        "Deploy automated recommendations"
      ],
      status: "planned"
    }
  ]

  const successMetrics = [
    { metric: "Liquidation Reduction", target: "40%", current: "28%", status: "on-track" },
    { metric: "User Retention", target: "85%", current: "78%", status: "on-track" },
    { metric: "Bad Debt Ratio", target: "<2%", current: "3.2%", status: "needs-improvement" },
    { metric: "Protocol Revenue", target: "+25%", current: "+18%", status: "on-track" }
  ]

  return (
    <div className="p-4 sm:p-6 space-y-4 sm:space-y-6 bg-background min-h-full">
      {/* Header */}
      <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex-1">
            <h1 className="text-xl sm:text-2xl font-bold text-text-primary mb-2">
              Ideal zScore Implementation Framework
            </h1>
            <p className="text-text-secondary mb-4 text-sm sm:text-base">
              Comprehensive best practices, implementation guidelines, and success metrics
              for optimal zScore integration in DeFi lending protocols.
            </p>
          </div>
          <div className="bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20 rounded-lg p-4 lg:ml-4 flex-shrink-0">
            <Award className="w-8 h-8 text-primary mx-auto mb-2" />
            <p className="text-sm font-medium text-text-primary text-center">Success Rate</p>
            <p className="text-2xl font-bold text-primary text-center">92%</p>
            <p className="text-xs text-text-secondary text-center">Implementation success</p>
          </div>
        </div>
      </div>

      {/* Best Practices */}
      <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-6">Best Practices by Category</h2>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
          {bestPractices.map((category, index) => {
            const Icon = category.icon
            return (
              <div key={index} className="bg-surface-light border border-border-color rounded-lg p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Icon className="w-5 h-5 text-primary" />
                  </div>
                  <h3 className="font-semibold text-text-primary">{category.category}</h3>
                </div>
                <ul className="space-y-3">
                  {category.practices.map((practice, practiceIndex) => (
                    <li key={practiceIndex} className="flex items-start space-x-3">
                      <CheckCircle className="w-4 h-4 text-secondary mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-text-secondary leading-relaxed">{practice}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )
          })}
        </div>
      </div>

      {/* Implementation Roadmap */}
      <div className="bg-surface border border-border-color rounded-xl p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-6">Implementation Roadmap</h2>
        <div className="space-y-6">
          {implementationSteps.map((phase, index) => (
            <div key={index} className="relative">
              {index < implementationSteps.length - 1 && (
                <div className="absolute left-6 top-12 w-0.5 h-16 bg-border-color" />
              )}
              <div className="flex items-start space-x-4">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center border-2 ${
                  phase.status === 'completed' ? 'bg-secondary border-secondary' :
                  phase.status === 'in-progress' ? 'bg-accent border-accent' :
                  'bg-surface border-border-color'
                }`}>
                  {phase.status === 'completed' ? (
                    <CheckCircle className="w-6 h-6 text-white" />
                  ) : (
                    <span className="text-sm font-bold text-text-primary">{index + 1}</span>
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold text-text-primary">{phase.phase}</h3>
                    <span className="text-sm text-text-secondary">{phase.duration}</span>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-3">
                    {phase.tasks.map((task, taskIndex) => (
                      <div key={taskIndex} className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${
                          phase.status === 'completed' ? 'bg-secondary' :
                          phase.status === 'in-progress' ? 'bg-accent' :
                          'bg-surface-light'
                        }`} />
                        <span className="text-sm text-text-secondary">{task}</span>
                      </div>
                    ))}
                  </div>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                    phase.status === 'completed' ? 'bg-secondary/10 text-secondary border border-secondary/20' :
                    phase.status === 'in-progress' ? 'bg-accent/10 text-accent border border-accent/20' :
                    'bg-surface-light text-text-secondary border border-border-color'
                  }`}>
                    {phase.status === 'completed' ? 'Completed' :
                     phase.status === 'in-progress' ? 'In Progress' : 'Planned'}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Success Metrics */}
      <div className="bg-surface border border-border-color rounded-xl p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-6">Success Metrics & KPIs</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {successMetrics.map((metric, index) => (
            <div key={index} className="bg-surface-light rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-medium text-text-primary">{metric.metric}</h3>
                <span className={`w-3 h-3 rounded-full ${
                  metric.status === 'on-track' ? 'bg-secondary' :
                  metric.status === 'needs-improvement' ? 'bg-accent' :
                  'bg-danger'
                }`} />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-text-secondary">Target</span>
                  <span className="text-sm font-medium text-text-primary">{metric.target}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-secondary">Current</span>
                  <span className="text-sm font-medium text-text-primary">{metric.current}</span>
                </div>
                <div className="w-full bg-surface rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      metric.status === 'on-track' ? 'bg-secondary' :
                      metric.status === 'needs-improvement' ? 'bg-accent' :
                      'bg-danger'
                    }`}
                    style={{ width: '75%' }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Key Recommendations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-surface border border-border-color rounded-xl p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Lightbulb className="w-6 h-6 text-accent" />
            <h3 className="text-lg font-semibold text-text-primary">Key Recommendations</h3>
          </div>
          <div className="space-y-3">
            <div className="bg-surface-light rounded-lg p-4">
              <p className="text-sm font-medium text-text-primary mb-2">Start with Conservative Parameters</p>
              <p className="text-xs text-text-secondary">
                Begin with higher collateral ratios and gradually optimize based on performance data
              </p>
            </div>
            <div className="bg-surface-light rounded-lg p-4">
              <p className="text-sm font-medium text-text-primary mb-2">Implement Gradual Rollout</p>
              <p className="text-xs text-text-secondary">
                Deploy to a subset of users first, then expand based on success metrics
              </p>
            </div>
            <div className="bg-surface-light rounded-lg p-4">
              <p className="text-sm font-medium text-text-primary mb-2">Focus on User Education</p>
              <p className="text-xs text-text-secondary">
                Provide clear explanations of how zScore affects lending terms and opportunities
              </p>
            </div>
          </div>
        </div>

        <div className="bg-surface border border-border-color rounded-xl p-6">
          <div className="flex items-center space-x-3 mb-4">
            <BookOpen className="w-6 h-6 text-primary" />
            <h3 className="text-lg font-semibold text-text-primary">Resources & Documentation</h3>
          </div>
          <div className="space-y-3">
            <a href="#" className="block bg-surface-light rounded-lg p-4 hover:bg-surface-light/80 transition-colors">
              <p className="text-sm font-medium text-text-primary mb-1">Implementation Guide</p>
              <p className="text-xs text-text-secondary">Step-by-step technical documentation</p>
            </a>
            <a href="#" className="block bg-surface-light rounded-lg p-4 hover:bg-surface-light/80 transition-colors">
              <p className="text-sm font-medium text-text-primary mb-1">API Documentation</p>
              <p className="text-xs text-text-secondary">Complete API reference and examples</p>
            </a>
            <a href="#" className="block bg-surface-light rounded-lg p-4 hover:bg-surface-light/80 transition-colors">
              <p className="text-sm font-medium text-text-primary mb-1">Case Studies</p>
              <p className="text-xs text-text-secondary">Real-world implementation examples</p>
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
