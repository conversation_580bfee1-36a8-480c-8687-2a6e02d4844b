'use client'

import { Activity, Bell, Search, User } from 'lucide-react'

export default function Header() {
  return (
    <header className="bg-surface border-b border-border-color px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side - Logo and title */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-primary to-primary-light rounded-lg flex items-center justify-center">
              <Activity className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-text-primary">Zeru</h1>
              <p className="text-sm text-text-secondary">Trust. Measured. Reputation Scored.</p>
            </div>
          </div>
        </div>

        {/* Center - Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          <a href="#" className="text-primary font-medium border-b-2 border-primary pb-1">
            Dashboard
          </a>
          <a href="#" className="text-text-secondary hover:text-text-primary transition-colors">
            Wallets
          </a>
          <a href="#" className="text-text-secondary hover:text-text-primary transition-colors">
            API Analytics
          </a>
          <a href="#" className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-light transition-colors">
            Connect Wallet
          </a>
        </nav>

        {/* Right side - Search and user actions */}
        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative hidden lg:block">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-muted w-4 h-4" />
            <input
              type="text"
              placeholder="Search wallets..."
              className="bg-surface-light border border-border-color rounded-lg pl-10 pr-4 py-2 text-sm text-text-primary placeholder-text-muted focus:outline-none focus:border-primary"
            />
          </div>

          {/* Notifications */}
          <button className="relative p-2 text-text-secondary hover:text-text-primary transition-colors">
            <Bell className="w-5 h-5" />
            <span className="absolute top-1 right-1 w-2 h-2 bg-danger rounded-full"></span>
          </button>

          {/* User profile */}
          <button className="flex items-center space-x-2 p-2 rounded-lg hover:bg-surface-light transition-colors">
            <div className="w-8 h-8 bg-gradient-to-r from-secondary to-accent rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-white" />
            </div>
            <span className="hidden md:block text-sm text-text-primary">Admin</span>
          </button>
        </div>
      </div>
    </header>
  )
}
