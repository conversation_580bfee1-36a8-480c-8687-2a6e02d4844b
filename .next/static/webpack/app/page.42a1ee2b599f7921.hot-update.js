"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/charts/RiskDistributionChart.tsx":
/*!*********************************************************!*\
  !*** ./src/components/charts/RiskDistributionChart.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RiskDistributionChart; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _barrel_optimize_names_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _data_liquidationData__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/data/liquidationData */ \"(app-pages-browser)/./src/data/liquidationData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction RiskDistributionChart() {\n    const data = _data_liquidationData__WEBPACK_IMPORTED_MODULE_1__.riskCategories.map((category)=>({\n            name: category.name,\n            value: category.percentage,\n            color: category.color,\n            range: category.range\n        }));\n    const CustomTooltip = (param)=>{\n        let { active, payload } = param;\n        if (active && payload && payload.length) {\n            const data = payload[0].payload;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-lg p-3 shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-text-primary font-medium\",\n                        children: data.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-text-secondary text-sm\",\n                        children: [\n                            \"Range: \",\n                            data.range\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-primary font-semibold\",\n                        children: [\n                            data.value,\n                            \"% of liquidations\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this);\n        }\n        return null;\n    };\n    const CustomLegend = (param)=>{\n        let { payload } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-wrap justify-center gap-4 mt-4\",\n            children: payload.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-3 h-3 rounded-full\",\n                            style: {\n                                backgroundColor: entry.color\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-text-secondary\",\n                            children: entry.value\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, index, true, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 sm:mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-base sm:text-lg font-semibold text-text-primary mb-2\",\n                        children: \"Wallet Population Score Distribution\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-text-secondary\",\n                        children: \"Risk assessment based on zScore ranges showing liquidation probability\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-64 sm:h-80\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_2__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: \"100%\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_3__.PieChart, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_4__.Pie, {\n                                data: data,\n                                cx: \"50%\",\n                                cy: \"50%\",\n                                innerRadius: 60,\n                                outerRadius: 120,\n                                paddingAngle: 2,\n                                dataKey: \"value\",\n                                children: data.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_5__.Cell, {\n                                        fill: entry.color\n                                    }, \"cell-\".concat(index), false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_6__.Tooltip, {\n                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 31\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_7__.Legend, {\n                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomLegend, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 30\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-4 mt-6\",\n                children: _data_liquidationData__WEBPACK_IMPORTED_MODULE_1__.riskCategories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-surface-light rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 rounded-full\",\n                                        style: {\n                                            backgroundColor: category.color\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-text-primary\",\n                                                children: category.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-text-secondary\",\n                                                children: category.range\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-bold text-text-primary mt-2\",\n                                children: [\n                                    category.percentage,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/RiskDistributionChart.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_c = RiskDistributionChart;\nvar _c;\n$RefreshReg$(_c, \"RiskDistributionChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NoYXJ0cy9SaXNrRGlzdHJpYnV0aW9uQ2hhcnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFb0Y7QUFDN0I7QUFFeEMsU0FBU087SUFDdEIsTUFBTUMsT0FBT0YsaUVBQWNBLENBQUNHLEdBQUcsQ0FBQ0MsQ0FBQUEsV0FBYTtZQUMzQ0MsTUFBTUQsU0FBU0MsSUFBSTtZQUNuQkMsT0FBT0YsU0FBU0csVUFBVTtZQUMxQkMsT0FBT0osU0FBU0ksS0FBSztZQUNyQkMsT0FBT0wsU0FBU0ssS0FBSztRQUN2QjtJQUVBLE1BQU1DLGdCQUFnQjtZQUFDLEVBQUVDLE1BQU0sRUFBRUMsT0FBTyxFQUFPO1FBQzdDLElBQUlELFVBQVVDLFdBQVdBLFFBQVFDLE1BQU0sRUFBRTtZQUN2QyxNQUFNWCxPQUFPVSxPQUFPLENBQUMsRUFBRSxDQUFDQSxPQUFPO1lBQy9CLHFCQUNFLDhEQUFDRTtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFFRCxXQUFVO2tDQUFpQ2IsS0FBS0csSUFBSTs7Ozs7O2tDQUN2RCw4REFBQ1c7d0JBQUVELFdBQVU7OzRCQUE4Qjs0QkFBUWIsS0FBS08sS0FBSzs7Ozs7OztrQ0FDN0QsOERBQUNPO3dCQUFFRCxXQUFVOzs0QkFBOEJiLEtBQUtJLEtBQUs7NEJBQUM7Ozs7Ozs7Ozs7Ozs7UUFHNUQ7UUFDQSxPQUFPO0lBQ1Q7SUFFQSxNQUFNVyxlQUFlO1lBQUMsRUFBRUwsT0FBTyxFQUFPO1FBQ3BDLHFCQUNFLDhEQUFDRTtZQUFJQyxXQUFVO3NCQUNaSCxRQUFRVCxHQUFHLENBQUMsQ0FBQ2UsT0FBWUMsc0JBQ3hCLDhEQUFDTDtvQkFBZ0JDLFdBQVU7O3NDQUN6Qiw4REFBQ0Q7NEJBQ0NDLFdBQVU7NEJBQ1ZLLE9BQU87Z0NBQUVDLGlCQUFpQkgsTUFBTVYsS0FBSzs0QkFBQzs7Ozs7O3NDQUV4Qyw4REFBQ2M7NEJBQUtQLFdBQVU7c0NBQStCRyxNQUFNWixLQUFLOzs7Ozs7O21CQUxsRGE7Ozs7Ozs7Ozs7SUFVbEI7SUFFQSxxQkFDRSw4REFBQ0w7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ1E7d0JBQUdSLFdBQVU7a0NBQTREOzs7Ozs7a0NBRzFFLDhEQUFDQzt3QkFBRUQsV0FBVTtrQ0FBOEI7Ozs7Ozs7Ozs7OzswQkFLN0MsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDbEIscUlBQW1CQTtvQkFBQzJCLE9BQU07b0JBQU9DLFFBQU87OEJBQ3ZDLDRFQUFDL0IsMEhBQVFBOzswQ0FDUCw4REFBQ0MscUhBQUdBO2dDQUNGTyxNQUFNQTtnQ0FDTndCLElBQUc7Z0NBQ0hDLElBQUc7Z0NBQ0hDLGFBQWE7Z0NBQ2JDLGFBQWE7Z0NBQ2JDLGNBQWM7Z0NBQ2RDLFNBQVE7MENBRVA3QixLQUFLQyxHQUFHLENBQUMsQ0FBQ2UsT0FBT0Msc0JBQ2hCLDhEQUFDdkIsc0hBQUlBO3dDQUF1Qm9DLE1BQU1kLE1BQU1WLEtBQUs7dUNBQWxDLFFBQWMsT0FBTlc7Ozs7Ozs7Ozs7MENBR3ZCLDhEQUFDcEIseUhBQU9BO2dDQUFDa0MsdUJBQVMsOERBQUN2Qjs7Ozs7Ozs7OzswQ0FDbkIsOERBQUNaLHdIQUFNQTtnQ0FBQ21DLHVCQUFTLDhEQUFDaEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUt4Qiw4REFBQ0g7Z0JBQUlDLFdBQVU7MEJBQ1pmLGlFQUFjQSxDQUFDRyxHQUFHLENBQUMsQ0FBQ0MsVUFBVWUsc0JBQzdCLDhEQUFDTDt3QkFBZ0JDLFdBQVU7OzBDQUN6Qiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FDQ0MsV0FBVTt3Q0FDVkssT0FBTzs0Q0FBRUMsaUJBQWlCakIsU0FBU0ksS0FBSzt3Q0FBQzs7Ozs7O2tEQUUzQyw4REFBQ007OzBEQUNDLDhEQUFDRTtnREFBRUQsV0FBVTswREFBeUNYLFNBQVNDLElBQUk7Ozs7OzswREFDbkUsOERBQUNXO2dEQUFFRCxXQUFVOzBEQUErQlgsU0FBU0ssS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUc5RCw4REFBQ087Z0NBQUVELFdBQVU7O29DQUNWWCxTQUFTRyxVQUFVO29DQUFDOzs7Ozs7Ozt1QkFaZlk7Ozs7Ozs7Ozs7Ozs7Ozs7QUFtQnBCO0tBNUZ3QmxCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2NoYXJ0cy9SaXNrRGlzdHJpYnV0aW9uQ2hhcnQudHN4PzNiMjUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFBpZUNoYXJ0LCBQaWUsIENlbGwsIFJlc3BvbnNpdmVDb250YWluZXIsIExlZ2VuZCwgVG9vbHRpcCB9IGZyb20gJ3JlY2hhcnRzJ1xuaW1wb3J0IHsgcmlza0NhdGVnb3JpZXMgfSBmcm9tICdAL2RhdGEvbGlxdWlkYXRpb25EYXRhJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSaXNrRGlzdHJpYnV0aW9uQ2hhcnQoKSB7XG4gIGNvbnN0IGRhdGEgPSByaXNrQ2F0ZWdvcmllcy5tYXAoY2F0ZWdvcnkgPT4gKHtcbiAgICBuYW1lOiBjYXRlZ29yeS5uYW1lLFxuICAgIHZhbHVlOiBjYXRlZ29yeS5wZXJjZW50YWdlLFxuICAgIGNvbG9yOiBjYXRlZ29yeS5jb2xvcixcbiAgICByYW5nZTogY2F0ZWdvcnkucmFuZ2VcbiAgfSkpXG5cbiAgY29uc3QgQ3VzdG9tVG9vbHRpcCA9ICh7IGFjdGl2ZSwgcGF5bG9hZCB9OiBhbnkpID0+IHtcbiAgICBpZiAoYWN0aXZlICYmIHBheWxvYWQgJiYgcGF5bG9hZC5sZW5ndGgpIHtcbiAgICAgIGNvbnN0IGRhdGEgPSBwYXlsb2FkWzBdLnBheWxvYWRcbiAgICAgIHJldHVybiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc3VyZmFjZSBib3JkZXIgYm9yZGVyLWJvcmRlci1jb2xvciByb3VuZGVkLWxnIHAtMyBzaGFkb3ctbGdcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXRleHQtcHJpbWFyeSBmb250LW1lZGl1bVwiPntkYXRhLm5hbWV9PC9wPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtdGV4dC1zZWNvbmRhcnkgdGV4dC1zbVwiPlJhbmdlOiB7ZGF0YS5yYW5nZX08L3A+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5IGZvbnQtc2VtaWJvbGRcIj57ZGF0YS52YWx1ZX0lIG9mIGxpcXVpZGF0aW9uczwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICApXG4gICAgfVxuICAgIHJldHVybiBudWxsXG4gIH1cblxuICBjb25zdCBDdXN0b21MZWdlbmQgPSAoeyBwYXlsb2FkIH06IGFueSkgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGp1c3RpZnktY2VudGVyIGdhcC00IG10LTRcIj5cbiAgICAgICAge3BheWxvYWQubWFwKChlbnRyeTogYW55LCBpbmRleDogbnVtYmVyKSA9PiAoXG4gICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0zIGgtMyByb3VuZGVkLWZ1bGxcIlxuICAgICAgICAgICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6IGVudHJ5LmNvbG9yIH19XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXRleHQtc2Vjb25kYXJ5XCI+e2VudHJ5LnZhbHVlfTwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc3VyZmFjZSBib3JkZXIgYm9yZGVyLWJvcmRlci1jb2xvciByb3VuZGVkLXhsIHAtNCBzbTpwLTZcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNCBzbTptYi02XCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWJhc2Ugc206dGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtdGV4dC1wcmltYXJ5IG1iLTJcIj5cbiAgICAgICAgICBXYWxsZXQgUG9wdWxhdGlvbiBTY29yZSBEaXN0cmlidXRpb25cbiAgICAgICAgPC9oMz5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXRleHQtc2Vjb25kYXJ5XCI+XG4gICAgICAgICAgUmlzayBhc3Nlc3NtZW50IGJhc2VkIG9uIHpTY29yZSByYW5nZXMgc2hvd2luZyBsaXF1aWRhdGlvbiBwcm9iYWJpbGl0eVxuICAgICAgICA8L3A+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTY0IHNtOmgtODBcIj5cbiAgICAgICAgPFJlc3BvbnNpdmVDb250YWluZXIgd2lkdGg9XCIxMDAlXCIgaGVpZ2h0PVwiMTAwJVwiPlxuICAgICAgICAgIDxQaWVDaGFydD5cbiAgICAgICAgICAgIDxQaWVcbiAgICAgICAgICAgICAgZGF0YT17ZGF0YX1cbiAgICAgICAgICAgICAgY3g9XCI1MCVcIlxuICAgICAgICAgICAgICBjeT1cIjUwJVwiXG4gICAgICAgICAgICAgIGlubmVyUmFkaXVzPXs2MH1cbiAgICAgICAgICAgICAgb3V0ZXJSYWRpdXM9ezEyMH1cbiAgICAgICAgICAgICAgcGFkZGluZ0FuZ2xlPXsyfVxuICAgICAgICAgICAgICBkYXRhS2V5PVwidmFsdWVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7ZGF0YS5tYXAoKGVudHJ5LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgIDxDZWxsIGtleT17YGNlbGwtJHtpbmRleH1gfSBmaWxsPXtlbnRyeS5jb2xvcn0gLz5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L1BpZT5cbiAgICAgICAgICAgIDxUb29sdGlwIGNvbnRlbnQ9ezxDdXN0b21Ub29sdGlwIC8+fSAvPlxuICAgICAgICAgICAgPExlZ2VuZCBjb250ZW50PXs8Q3VzdG9tTGVnZW5kIC8+fSAvPlxuICAgICAgICAgIDwvUGllQ2hhcnQ+XG4gICAgICAgIDwvUmVzcG9uc2l2ZUNvbnRhaW5lcj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTQgbXQtNlwiPlxuICAgICAgICB7cmlza0NhdGVnb3JpZXMubWFwKChjYXRlZ29yeSwgaW5kZXgpID0+IChcbiAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImJnLXN1cmZhY2UtbGlnaHQgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWQtZnVsbFwiXG4gICAgICAgICAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiBjYXRlZ29yeS5jb2xvciB9fVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC10ZXh0LXByaW1hcnlcIj57Y2F0ZWdvcnkubmFtZX08L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXRleHQtc2Vjb25kYXJ5XCI+e2NhdGVnb3J5LnJhbmdlfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtdGV4dC1wcmltYXJ5IG10LTJcIj5cbiAgICAgICAgICAgICAge2NhdGVnb3J5LnBlcmNlbnRhZ2V9JVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiUGllQ2hhcnQiLCJQaWUiLCJDZWxsIiwiUmVzcG9uc2l2ZUNvbnRhaW5lciIsIkxlZ2VuZCIsIlRvb2x0aXAiLCJyaXNrQ2F0ZWdvcmllcyIsIlJpc2tEaXN0cmlidXRpb25DaGFydCIsImRhdGEiLCJtYXAiLCJjYXRlZ29yeSIsIm5hbWUiLCJ2YWx1ZSIsInBlcmNlbnRhZ2UiLCJjb2xvciIsInJhbmdlIiwiQ3VzdG9tVG9vbHRpcCIsImFjdGl2ZSIsInBheWxvYWQiLCJsZW5ndGgiLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwiQ3VzdG9tTGVnZW5kIiwiZW50cnkiLCJpbmRleCIsInN0eWxlIiwiYmFja2dyb3VuZENvbG9yIiwic3BhbiIsImgzIiwid2lkdGgiLCJoZWlnaHQiLCJjeCIsImN5IiwiaW5uZXJSYWRpdXMiLCJvdXRlclJhZGl1cyIsInBhZGRpbmdBbmdsZSIsImRhdGFLZXkiLCJmaWxsIiwiY29udGVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/charts/RiskDistributionChart.tsx\n"));

/***/ })

});