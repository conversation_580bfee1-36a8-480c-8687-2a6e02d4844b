"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/lending/Global.tsx":
/*!*******************************************!*\
  !*** ./src/components/lending/Global.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LendingGlobal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Globe,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Globe,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Globe,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Globe,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Globe,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _components_charts_MetricCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/charts/MetricCard */ \"(app-pages-browser)/./src/components/charts/MetricCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction LendingGlobal() {\n    const globalMetrics = [\n        {\n            region: \"North America\",\n            volume: \"$2.4B\",\n            growth: \"+15.2%\",\n            users: \"145K\"\n        },\n        {\n            region: \"Europe\",\n            volume: \"$1.8B\",\n            growth: \"+12.8%\",\n            users: \"98K\"\n        },\n        {\n            region: \"Asia Pacific\",\n            volume: \"$3.1B\",\n            growth: \"+28.4%\",\n            users: \"234K\"\n        },\n        {\n            region: \"Latin America\",\n            volume: \"$0.6B\",\n            growth: \"+45.1%\",\n            users: \"67K\"\n        }\n    ];\n    const protocolMetrics = [\n        {\n            protocol: \"Aave\",\n            tvl: \"$8.2B\",\n            users: \"156K\",\n            chains: 8\n        },\n        {\n            protocol: \"Compound\",\n            tvl: \"$3.4B\",\n            users: \"89K\",\n            chains: 3\n        },\n        {\n            protocol: \"MakerDAO\",\n            tvl: \"$5.1B\",\n            users: \"67K\",\n            chains: 1\n        },\n        {\n            protocol: \"Venus\",\n            tvl: \"$1.2B\",\n            users: \"34K\",\n            chains: 2\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 sm:p-6 space-y-4 sm:space-y-6 bg-background min-h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-start lg:justify-between space-y-4 lg:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-text-primary mb-2\",\n                                    children: \"Global DeFi Lending Landscape\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-text-secondary mb-4 text-sm sm:text-base\",\n                                    children: \"Worldwide analysis of DeFi lending protocols, regional adoption patterns, and global zScore implementation across different markets and regulatory environments.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-secondary/10 to-accent/10 border border-secondary/20 rounded-lg p-4 lg:ml-4 flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-8 h-8 text-secondary mx-auto mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-text-primary text-center\",\n                                    children: \"Global Reach\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-secondary text-center\",\n                                    children: \"180+\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-text-secondary text-center\",\n                                    children: \"Countries\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_MetricCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        title: \"Total Value Locked\",\n                        value: \"$18.7B\",\n                        change: \"+18.5% this quarter\",\n                        changeType: \"positive\",\n                        icon: _barrel_optimize_names_Activity_BarChart3_DollarSign_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                        description: \"Across all protocols\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_MetricCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        title: \"Active Users\",\n                        value: \"544K\",\n                        change: \"+23.1% growth\",\n                        changeType: \"positive\",\n                        icon: _barrel_optimize_names_Activity_BarChart3_DollarSign_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                        description: \"Monthly active borrowers\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_MetricCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        title: \"Supported Chains\",\n                        value: \"25\",\n                        change: \"+5 new chains\",\n                        changeType: \"positive\",\n                        icon: _barrel_optimize_names_Activity_BarChart3_DollarSign_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                        description: \"Multi-chain deployment\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_MetricCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        title: \"Daily Volume\",\n                        value: \"$2.1B\",\n                        change: \"+12.3% increase\",\n                        changeType: \"positive\",\n                        icon: _barrel_optimize_names_Activity_BarChart3_DollarSign_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                        description: \"24h lending volume\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6\",\n                        children: \"Regional Market Analysis\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: globalMetrics.map((region, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-text-primary mb-3\",\n                                        children: region.region\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Volume\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-text-primary\",\n                                                        children: region.volume\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Growth\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: region.growth\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Users\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-text-primary\",\n                                                        children: region.users\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6\",\n                        children: \"Major Protocol Metrics\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b border-border-color\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-sm font-medium text-text-secondary\",\n                                                children: \"Protocol\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-sm font-medium text-text-secondary\",\n                                                children: \"TVL\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-sm font-medium text-text-secondary\",\n                                                children: \"Active Users\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-sm font-medium text-text-secondary\",\n                                                children: \"Chains\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-sm font-medium text-text-secondary\",\n                                                children: \"zScore Integration\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: protocolMetrics.map((protocol, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-border-color/50 hover:bg-surface-light/50 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-primary text-xs font-bold\",\n                                                                    children: protocol.protocol.charAt(0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-text-primary\",\n                                                                children: protocol.protocol\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                                lineNumber: 130,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4 text-text-primary font-medium\",\n                                                    children: protocol.tvl\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4 text-text-primary\",\n                                                    children: protocol.users\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4 text-text-primary\",\n                                                    children: protocol.chains\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium \".concat(index === 0 ? \"bg-secondary/10 text-secondary border border-secondary/20\" : index === 1 ? \"bg-accent/10 text-accent border border-accent/20\" : \"bg-surface-light text-text-secondary border border-border-color\"),\n                                                        children: index === 0 ? \"Live\" : index === 1 ? \"Beta\" : \"Planned\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-surface border border-border-color rounded-xl p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-text-primary mb-4\",\n                                children: \"Adoption Trends\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-text-secondary\",\n                                                children: \"Institutional Adoption\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-24 h-2 bg-surface-light rounded-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-2 bg-primary rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-primary\",\n                                                        children: \"67%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-text-secondary\",\n                                                children: \"Retail Participation\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-24 h-2 bg-surface-light rounded-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-20 h-2 bg-secondary rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-primary\",\n                                                        children: \"83%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-text-secondary\",\n                                                children: \"Cross-chain Usage\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-24 h-2 bg-surface-light rounded-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-2 bg-accent rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-primary\",\n                                                        children: \"45%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-surface border border-border-color rounded-xl p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-text-primary mb-4\",\n                                children: \"Regulatory Landscape\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-surface-light rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-text-primary\",\n                                                        children: \"EU MiCA Compliance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-text-secondary\",\n                                                        children: \"Markets in Crypto-Assets\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs bg-secondary/10 text-secondary px-2 py-1 rounded border border-secondary/20\",\n                                                children: \"Compliant\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-surface-light rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-text-primary\",\n                                                        children: \"US Regulatory Clarity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-text-secondary\",\n                                                        children: \"State-by-state approach\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs bg-accent/10 text-accent px-2 py-1 rounded border border-accent/20\",\n                                                children: \"Pending\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-surface-light rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-text-primary\",\n                                                        children: \"APAC Framework\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-text-secondary\",\n                                                        children: \"Singapore, Japan leading\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs bg-secondary/10 text-secondary px-2 py-1 rounded border border-secondary/20\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Global.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n_c = LendingGlobal;\nvar _c;\n$RefreshReg$(_c, \"LendingGlobal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/lending/Global.tsx\n"));

/***/ })

});