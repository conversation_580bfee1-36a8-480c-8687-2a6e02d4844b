"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/charts/ChainDistributionChart.tsx":
/*!**********************************************************!*\
  !*** ./src/components/charts/ChainDistributionChart.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChainDistributionChart; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _data_liquidationData__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/data/liquidationData */ \"(app-pages-browser)/./src/data/liquidationData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ChainDistributionChart() {\n    const CustomTooltip = (param)=>{\n        let { active, payload, label } = param;\n        if (active && payload && payload.length) {\n            const data = payload[0].payload;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-lg p-3 shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-text-primary font-medium\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-primary font-semibold\",\n                        children: [\n                            data.value.toLocaleString(),\n                            \" liquidations\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-text-secondary text-sm\",\n                        children: [\n                            data.percentage,\n                            \"% of total\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                lineNumber: 11,\n                columnNumber: 9\n            }, this);\n        }\n        return null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 sm:mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-base sm:text-lg font-semibold text-text-primary mb-2\",\n                        children: \"Chains Distribution\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-text-secondary\",\n                        children: \"Liquidation events across different blockchain networks\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-64 sm:h-80\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: \"100%\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.BarChart, {\n                        data: _data_liquidationData__WEBPACK_IMPORTED_MODULE_1__.chainDistribution,\n                        margin: {\n                            top: 20,\n                            right: 30,\n                            left: 20,\n                            bottom: 5\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.CartesianGrid, {\n                                strokeDasharray: \"3 3\",\n                                stroke: \"#334155\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.XAxis, {\n                                dataKey: \"name\",\n                                stroke: \"#94A3B8\",\n                                fontSize: 12,\n                                angle: -45,\n                                textAnchor: \"end\",\n                                height: 80\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.YAxis, {\n                                stroke: \"#94A3B8\",\n                                fontSize: 12\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 31\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.Bar, {\n                                dataKey: \"value\",\n                                fill: \"#6366F1\",\n                                radius: [\n                                    4,\n                                    4,\n                                    0,\n                                    0\n                                ]\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 mt-6\",\n                children: _data_liquidationData__WEBPACK_IMPORTED_MODULE_1__.chainDistribution.slice(0, 3).map((chain, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-surface-light rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 rounded-full\",\n                                        style: {\n                                            backgroundColor: chain.color\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-text-primary\",\n                                                children: chain.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-text-secondary\",\n                                                children: [\n                                                    chain.percentage,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-bold text-text-primary mt-2\",\n                                children: chain.value.toLocaleString()\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/ChainDistributionChart.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n_c = ChainDistributionChart;\nvar _c;\n$RefreshReg$(_c, \"ChainDistributionChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/charts/ChainDistributionChart.tsx\n"));

/***/ })

});