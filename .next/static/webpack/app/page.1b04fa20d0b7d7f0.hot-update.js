"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/lending/Ideal.tsx":
/*!******************************************!*\
  !*** ./src/components/lending/Ideal.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LendingIdeal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_CheckCircle_Lightbulb_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,CheckCircle,Lightbulb,Shield,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_CheckCircle_Lightbulb_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,CheckCircle,Lightbulb,Shield,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_CheckCircle_Lightbulb_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,CheckCircle,Lightbulb,Shield,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_CheckCircle_Lightbulb_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,CheckCircle,Lightbulb,Shield,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_CheckCircle_Lightbulb_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,CheckCircle,Lightbulb,Shield,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_CheckCircle_Lightbulb_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,CheckCircle,Lightbulb,Shield,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_CheckCircle_Lightbulb_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,CheckCircle,Lightbulb,Shield,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LendingIdeal() {\n    const bestPractices = [\n        {\n            category: \"Risk Assessment\",\n            icon: _barrel_optimize_names_Award_BookOpen_CheckCircle_Lightbulb_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            practices: [\n                \"Implement multi-layered zScore validation with real-time updates\",\n                \"Use dynamic collateral ratios based on wallet reputation scores\",\n                \"Deploy automated liquidation protection for high-risk positions\",\n                \"Integrate cross-chain reputation data for comprehensive assessment\"\n            ]\n        },\n        {\n            category: \"User Experience\",\n            icon: _barrel_optimize_names_Award_BookOpen_CheckCircle_Lightbulb_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            practices: [\n                \"Provide transparent zScore explanations to users\",\n                \"Offer reputation improvement guidance and recommendations\",\n                \"Implement gradual access to higher lending limits\",\n                \"Create educational content about reputation building\"\n            ]\n        },\n        {\n            category: \"Protocol Design\",\n            icon: _barrel_optimize_names_Award_BookOpen_CheckCircle_Lightbulb_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            practices: [\n                \"Design incentive mechanisms for positive reputation building\",\n                \"Implement reputation-based fee structures\",\n                \"Create governance participation rewards for high-reputation users\",\n                \"Build reputation recovery mechanisms for reformed users\"\n            ]\n        }\n    ];\n    const implementationSteps = [\n        {\n            phase: \"Phase 1: Foundation\",\n            duration: \"2-3 months\",\n            tasks: [\n                \"Deploy basic zScore integration\",\n                \"Implement risk categorization\",\n                \"Set up monitoring infrastructure\",\n                \"Create user dashboard\"\n            ],\n            status: \"completed\"\n        },\n        {\n            phase: \"Phase 2: Enhancement\",\n            duration: \"3-4 months\",\n            tasks: [\n                \"Add cross-chain reputation tracking\",\n                \"Implement dynamic pricing\",\n                \"Deploy AI-powered risk models\",\n                \"Create reputation recovery system\"\n            ],\n            status: \"in-progress\"\n        },\n        {\n            phase: \"Phase 3: Optimization\",\n            duration: \"2-3 months\",\n            tasks: [\n                \"Fine-tune risk parameters\",\n                \"Implement advanced analytics\",\n                \"Add governance integration\",\n                \"Deploy automated recommendations\"\n            ],\n            status: \"planned\"\n        }\n    ];\n    const successMetrics = [\n        {\n            metric: \"Liquidation Reduction\",\n            target: \"40%\",\n            current: \"28%\",\n            status: \"on-track\"\n        },\n        {\n            metric: \"User Retention\",\n            target: \"85%\",\n            current: \"78%\",\n            status: \"on-track\"\n        },\n        {\n            metric: \"Bad Debt Ratio\",\n            target: \"<2%\",\n            current: \"3.2%\",\n            status: \"needs-improvement\"\n        },\n        {\n            metric: \"Protocol Revenue\",\n            target: \"+25%\",\n            current: \"+18%\",\n            status: \"on-track\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 sm:p-6 space-y-4 sm:space-y-6 bg-background min-h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-start lg:justify-between space-y-4 lg:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-text-primary mb-2\",\n                                    children: \"Ideal zScore Implementation Framework\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-text-secondary mb-4 text-sm sm:text-base\",\n                                    children: \"Comprehensive best practices, implementation guidelines, and success metrics for optimal zScore integration in DeFi lending protocols.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20 rounded-lg p-4 lg:ml-4 flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_CheckCircle_Lightbulb_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-8 h-8 text-primary mx-auto mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-text-primary text-center\",\n                                    children: \"Success Rate\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-primary text-center\",\n                                    children: \"92%\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-text-secondary text-center\",\n                                    children: \"Implementation success\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6\",\n                        children: \"Best Practices by Category\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: bestPractices.map((category, index)=>{\n                            const Icon = category.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light border border-border-color rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"w-5 h-5 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-text-primary\",\n                                                children: category.category\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: category.practices.map((practice, practiceIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_CheckCircle_Lightbulb_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4 text-secondary mt-0.5 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary leading-relaxed\",\n                                                        children: practice\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, practiceIndex, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6\",\n                        children: \"Implementation Roadmap\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: implementationSteps.map((phase, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    index < implementationSteps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-6 top-12 w-0.5 h-16 bg-border-color\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full flex items-center justify-center border-2 \".concat(phase.status === \"completed\" ? \"bg-secondary border-secondary\" : phase.status === \"in-progress\" ? \"bg-accent border-accent\" : \"bg-surface border-border-color\"),\n                                                children: phase.status === \"completed\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_CheckCircle_Lightbulb_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-bold text-text-primary\",\n                                                    children: index + 1\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-text-primary\",\n                                                                children: phase.phase\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-text-secondary\",\n                                                                children: phase.duration\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-2 mb-3\",\n                                                        children: phase.tasks.map((task, taskIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full \".concat(phase.status === \"completed\" ? \"bg-secondary\" : phase.status === \"in-progress\" ? \"bg-accent\" : \"bg-surface-light\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                                        lineNumber: 172,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-text-secondary\",\n                                                                        children: task\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                                        lineNumber: 177,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, taskIndex, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium \".concat(phase.status === \"completed\" ? \"bg-secondary/10 text-secondary border border-secondary/20\" : phase.status === \"in-progress\" ? \"bg-accent/10 text-accent border border-accent/20\" : \"bg-surface-light text-text-secondary border border-border-color\"),\n                                                        children: phase.status === \"completed\" ? \"Completed\" : phase.status === \"in-progress\" ? \"In Progress\" : \"Planned\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6\",\n                        children: \"Success Metrics & KPIs\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: successMetrics.map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-text-primary\",\n                                                children: metric.metric\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(metric.status === \"on-track\" ? \"bg-secondary\" : metric.status === \"needs-improvement\" ? \"bg-accent\" : \"bg-danger\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Target\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-text-primary\",\n                                                        children: metric.target\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Current\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-text-primary\",\n                                                        children: metric.current\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-surface rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 rounded-full \".concat(metric.status === \"on-track\" ? \"bg-secondary\" : metric.status === \"needs-improvement\" ? \"bg-accent\" : \"bg-danger\"),\n                                                    style: {\n                                                        width: \"75%\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-surface border border-border-color rounded-xl p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_CheckCircle_Lightbulb_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-6 h-6 text-accent\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-text-primary\",\n                                        children: \"Key Recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-surface-light rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-text-primary mb-2\",\n                                                children: \"Start with Conservative Parameters\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-text-secondary\",\n                                                children: \"Begin with higher collateral ratios and gradually optimize based on performance data\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-surface-light rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-text-primary mb-2\",\n                                                children: \"Implement Gradual Rollout\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-text-secondary\",\n                                                children: \"Deploy to a subset of users first, then expand based on success metrics\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-surface-light rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-text-primary mb-2\",\n                                                children: \"Focus on User Education\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-text-secondary\",\n                                                children: \"Provide clear explanations of how zScore affects lending terms and opportunities\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-surface border border-border-color rounded-xl p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_CheckCircle_Lightbulb_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-6 h-6 text-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-text-primary\",\n                                        children: \"Resources & Documentation\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"block bg-surface-light rounded-lg p-4 hover:bg-surface-light/80 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-text-primary mb-1\",\n                                                children: \"Implementation Guide\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-text-secondary\",\n                                                children: \"Step-by-step technical documentation\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"block bg-surface-light rounded-lg p-4 hover:bg-surface-light/80 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-text-primary mb-1\",\n                                                children: \"API Documentation\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-text-secondary\",\n                                                children: \"Complete API reference and examples\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"block bg-surface-light rounded-lg p-4 hover:bg-surface-light/80 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-text-primary mb-1\",\n                                                children: \"Case Studies\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-text-secondary\",\n                                                children: \"Real-world implementation examples\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Ideal.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_c = LendingIdeal;\nvar _c;\n$RefreshReg$(_c, \"LendingIdeal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/lending/Ideal.tsx\n"));

/***/ })

});