"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/charts/MetricCard.tsx":
/*!**********************************************!*\
  !*** ./src/components/charts/MetricCard.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MetricCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction MetricCard(param) {\n    let { title, value, change, changeType = \"neutral\", icon: Icon, description, className } = param;\n    const getChangeColor = ()=>{\n        switch(changeType){\n            case \"positive\":\n                return \"text-secondary\";\n            case \"negative\":\n                return \"text-danger\";\n            default:\n                return \"text-text-secondary\";\n        }\n    };\n    const getChangeIcon = ()=>{\n        if (!change) return null;\n        const isPositive = changeType === \"positive\";\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"text-xs\", getChangeColor()),\n            children: [\n                isPositive ? \"↗\" : changeType === \"negative\" ? \"↘\" : \"→\",\n                \" \",\n                change\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/MetricCard.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"bg-surface border border-border-color rounded-xl p-4 sm:p-6 card-hover\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 mb-2\",\n                        children: [\n                            Icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"w-4 h-4 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/MetricCard.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/MetricCard.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xs sm:text-sm font-medium text-text-secondary\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/MetricCard.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/MetricCard.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl sm:text-2xl font-bold text-text-primary\",\n                                children: value\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/MetricCard.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            change && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: getChangeIcon()\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/MetricCard.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, this),\n                            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-text-muted\",\n                                children: description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/MetricCard.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/MetricCard.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/MetricCard.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/MetricCard.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/charts/MetricCard.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_c = MetricCard;\nvar _c;\n$RefreshReg$(_c, \"MetricCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NoYXJ0cy9NZXRyaWNDYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBR2dDO0FBWWpCLFNBQVNDLFdBQVcsS0FRakI7UUFSaUIsRUFDakNDLEtBQUssRUFDTEMsS0FBSyxFQUNMQyxNQUFNLEVBQ05DLGFBQWEsU0FBUyxFQUN0QkMsTUFBTUMsSUFBSSxFQUNWQyxXQUFXLEVBQ1hDLFNBQVMsRUFDTyxHQVJpQjtJQVNqQyxNQUFNQyxpQkFBaUI7UUFDckIsT0FBUUw7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLE1BQU1NLGdCQUFnQjtRQUNwQixJQUFJLENBQUNQLFFBQVEsT0FBTztRQUNwQixNQUFNUSxhQUFhUCxlQUFlO1FBQ2xDLHFCQUNFLDhEQUFDUTtZQUFLSixXQUFXVCw4Q0FBRUEsQ0FBQyxXQUFXVTs7Z0JBQzVCRSxhQUFhLE1BQU1QLGVBQWUsYUFBYSxNQUFNO2dCQUFJO2dCQUFFRDs7Ozs7OztJQUdsRTtJQUVBLHFCQUNFLDhEQUFDVTtRQUFJTCxXQUFXVCw4Q0FBRUEsQ0FDaEIsMEVBQ0FTO2tCQUVBLDRFQUFDSztZQUFJTCxXQUFVO3NCQUNiLDRFQUFDSztnQkFBSUwsV0FBVTs7a0NBQ2IsOERBQUNLO3dCQUFJTCxXQUFVOzs0QkFDWkYsc0JBQ0MsOERBQUNPO2dDQUFJTCxXQUFVOzBDQUNiLDRFQUFDRjtvQ0FBS0UsV0FBVTs7Ozs7Ozs7Ozs7MENBR3BCLDhEQUFDTTtnQ0FBR04sV0FBVTswQ0FBc0RQOzs7Ozs7Ozs7Ozs7a0NBR3RFLDhEQUFDWTt3QkFBSUwsV0FBVTs7MENBQ2IsOERBQUNPO2dDQUFFUCxXQUFVOzBDQUFtRE47Ozs7Ozs0QkFDL0RDLHdCQUNDLDhEQUFDVTtnQ0FBSUwsV0FBVTswQ0FDWkU7Ozs7Ozs0QkFHSkgsNkJBQ0MsOERBQUNRO2dDQUFFUCxXQUFVOzBDQUEyQkQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPdEQ7S0E3RHdCUCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9jaGFydHMvTWV0cmljQ2FyZC50c3g/ZGFlMCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgTHVjaWRlSWNvbiB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9saWIvdXRpbHMnXG5cbmludGVyZmFjZSBNZXRyaWNDYXJkUHJvcHMge1xuICB0aXRsZTogc3RyaW5nXG4gIHZhbHVlOiBzdHJpbmcgfCBudW1iZXJcbiAgY2hhbmdlPzogc3RyaW5nXG4gIGNoYW5nZVR5cGU/OiAncG9zaXRpdmUnIHwgJ25lZ2F0aXZlJyB8ICduZXV0cmFsJ1xuICBpY29uPzogTHVjaWRlSWNvblxuICBkZXNjcmlwdGlvbj86IHN0cmluZ1xuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTWV0cmljQ2FyZCh7XG4gIHRpdGxlLFxuICB2YWx1ZSxcbiAgY2hhbmdlLFxuICBjaGFuZ2VUeXBlID0gJ25ldXRyYWwnLFxuICBpY29uOiBJY29uLFxuICBkZXNjcmlwdGlvbixcbiAgY2xhc3NOYW1lXG59OiBNZXRyaWNDYXJkUHJvcHMpIHtcbiAgY29uc3QgZ2V0Q2hhbmdlQ29sb3IgPSAoKSA9PiB7XG4gICAgc3dpdGNoIChjaGFuZ2VUeXBlKSB7XG4gICAgICBjYXNlICdwb3NpdGl2ZSc6XG4gICAgICAgIHJldHVybiAndGV4dC1zZWNvbmRhcnknXG4gICAgICBjYXNlICduZWdhdGl2ZSc6XG4gICAgICAgIHJldHVybiAndGV4dC1kYW5nZXInXG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gJ3RleHQtdGV4dC1zZWNvbmRhcnknXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0Q2hhbmdlSWNvbiA9ICgpID0+IHtcbiAgICBpZiAoIWNoYW5nZSkgcmV0dXJuIG51bGxcbiAgICBjb25zdCBpc1Bvc2l0aXZlID0gY2hhbmdlVHlwZSA9PT0gJ3Bvc2l0aXZlJ1xuICAgIHJldHVybiAoXG4gICAgICA8c3BhbiBjbGFzc05hbWU9e2NuKCd0ZXh0LXhzJywgZ2V0Q2hhbmdlQ29sb3IoKSl9PlxuICAgICAgICB7aXNQb3NpdGl2ZSA/ICfihpcnIDogY2hhbmdlVHlwZSA9PT0gJ25lZ2F0aXZlJyA/ICfihpgnIDogJ+KGkid9IHtjaGFuZ2V9XG4gICAgICA8L3NwYW4+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17Y24oXG4gICAgICAnYmctc3VyZmFjZSBib3JkZXIgYm9yZGVyLWJvcmRlci1jb2xvciByb3VuZGVkLXhsIHAtNCBzbTpwLTYgY2FyZC1ob3ZlcicsXG4gICAgICBjbGFzc05hbWVcbiAgICApfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtYi0yXCI+XG4gICAgICAgICAgICB7SWNvbiAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1wcmltYXJ5LzEwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8SWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtcHJpbWFyeVwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhzIHNtOnRleHQtc20gZm9udC1tZWRpdW0gdGV4dC10ZXh0LXNlY29uZGFyeVwiPnt0aXRsZX08L2gzPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgc206dGV4dC0yeGwgZm9udC1ib2xkIHRleHQtdGV4dC1wcmltYXJ5XCI+e3ZhbHVlfTwvcD5cbiAgICAgICAgICAgIHtjaGFuZ2UgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIHtnZXRDaGFuZ2VJY29uKCl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIHtkZXNjcmlwdGlvbiAmJiAoXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC10ZXh0LW11dGVkXCI+e2Rlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJjbiIsIk1ldHJpY0NhcmQiLCJ0aXRsZSIsInZhbHVlIiwiY2hhbmdlIiwiY2hhbmdlVHlwZSIsImljb24iLCJJY29uIiwiZGVzY3JpcHRpb24iLCJjbGFzc05hbWUiLCJnZXRDaGFuZ2VDb2xvciIsImdldENoYW5nZUljb24iLCJpc1Bvc2l0aXZlIiwic3BhbiIsImRpdiIsImgzIiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/charts/MetricCard.tsx\n"));

/***/ })

});