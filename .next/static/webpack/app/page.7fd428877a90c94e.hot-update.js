"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/TabSystem.tsx":
/*!**************************************!*\
  !*** ./src/components/TabSystem.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TabSystem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lending_RealUsecase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lending/RealUsecase */ \"(app-pages-browser)/./src/components/lending/RealUsecase.tsx\");\n/* harmony import */ var _lending_Global__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lending/Global */ \"(app-pages-browser)/./src/components/lending/Global.tsx\");\n/* harmony import */ var _lending_LLM__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lending/LLM */ \"(app-pages-browser)/./src/components/lending/LLM.tsx\");\n/* harmony import */ var _lending_Ideal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lending/Ideal */ \"(app-pages-browser)/./src/components/lending/Ideal.tsx\");\n/* harmony import */ var _ComingSoon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ComingSoon */ \"(app-pages-browser)/./src/components/ComingSoon.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst tabs = [\n    {\n        id: \"real-usecase\",\n        name: \"Real-usecase\",\n        description: \"Live data analysis\"\n    },\n    {\n        id: \"global\",\n        name: \"Global\",\n        description: \"Worldwide metrics\"\n    },\n    {\n        id: \"llm\",\n        name: \"LLM\",\n        description: \"AI insights\"\n    },\n    {\n        id: \"ideal\",\n        name: \"Ideal usecases\",\n        description: \"Best practices\"\n    }\n];\nfunction TabSystem(param) {\n    let { selectedSector, selectedTab, onTabChange } = param;\n    const renderContent = ()=>{\n        if (selectedSector === \"lending\") {\n            switch(selectedTab){\n                case \"real-usecase\":\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lending_RealUsecase__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 18\n                    }, this);\n                case \"global\":\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lending_Global__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 18\n                    }, this);\n                case \"llm\":\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lending_LLM__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 18\n                    }, this);\n                case \"ideal\":\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lending_Ideal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 18\n                    }, this);\n                default:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lending_RealUsecase__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 18\n                    }, this);\n            }\n        }\n        // For other sectors, show coming soon\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComingSoon__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            sector: selectedSector\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n            lineNumber: 41,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border-b border-border-color px-4 sm:px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg sm:text-xl font-semibold text-text-primary capitalize\",\n                                    children: [\n                                        selectedSector,\n                                        \" Analytics\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-text-secondary hidden sm:block\",\n                                    children: [\n                                        \"Comprehensive analysis and insights for \",\n                                        selectedSector,\n                                        \" use cases\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1 bg-surface-light rounded-lg p-1 overflow-x-auto\",\n                        children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onTabChange(tab.id),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex-1 min-w-0 px-2 sm:px-4 py-3 rounded-md text-xs sm:text-sm font-medium transition-all duration-200\", \"hover:bg-surface hover:text-text-primary whitespace-nowrap\", selectedTab === tab.id ? \"bg-primary text-white shadow-sm\" : \"text-text-secondary\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: tab.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs opacity-75 mt-1 hidden sm:block\",\n                                            children: tab.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            }, tab.id, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto\",\n                children: renderContent()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_c = TabSystem;\nvar _c;\n$RefreshReg$(_c, \"TabSystem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1RhYlN5c3RlbS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRWdDO0FBQ3NCO0FBQ1Y7QUFDTjtBQUNJO0FBQ0w7QUFRckMsTUFBTU0sT0FBTztJQUNYO1FBQUVDLElBQUk7UUFBZ0JDLE1BQU07UUFBZ0JDLGFBQWE7SUFBcUI7SUFDOUU7UUFBRUYsSUFBSTtRQUFVQyxNQUFNO1FBQVVDLGFBQWE7SUFBb0I7SUFDakU7UUFBRUYsSUFBSTtRQUFPQyxNQUFNO1FBQU9DLGFBQWE7SUFBYztJQUNyRDtRQUFFRixJQUFJO1FBQVNDLE1BQU07UUFBa0JDLGFBQWE7SUFBaUI7Q0FDdEU7QUFFYyxTQUFTQyxVQUFVLEtBQTREO1FBQTVELEVBQUVDLGNBQWMsRUFBRUMsV0FBVyxFQUFFQyxXQUFXLEVBQWtCLEdBQTVEO0lBQ2hDLE1BQU1DLGdCQUFnQjtRQUNwQixJQUFJSCxtQkFBbUIsV0FBVztZQUNoQyxPQUFRQztnQkFDTixLQUFLO29CQUNILHFCQUFPLDhEQUFDWCw0REFBa0JBOzs7OztnQkFDNUIsS0FBSztvQkFDSCxxQkFBTyw4REFBQ0MsdURBQWFBOzs7OztnQkFDdkIsS0FBSztvQkFDSCxxQkFBTyw4REFBQ0Msb0RBQVVBOzs7OztnQkFDcEIsS0FBSztvQkFDSCxxQkFBTyw4REFBQ0Msc0RBQVlBOzs7OztnQkFDdEI7b0JBQ0UscUJBQU8sOERBQUNILDREQUFrQkE7Ozs7O1lBQzlCO1FBQ0Y7UUFFQSxzQ0FBc0M7UUFDdEMscUJBQU8sOERBQUNJLG1EQUFVQTtZQUFDVSxRQUFRSjs7Ozs7O0lBQzdCO0lBRUEscUJBQ0UsOERBQUNLO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs7OENBQ0MsOERBQUNFO29DQUFHRCxXQUFVOzt3Q0FDWE47d0NBQWU7Ozs7Ozs7OENBRWxCLDhEQUFDUTtvQ0FBRUYsV0FBVTs7d0NBQThDO3dDQUNoQk47d0NBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLOUQsOERBQUNLO3dCQUFJQyxXQUFVO2tDQUNaWCxLQUFLYyxHQUFHLENBQUMsQ0FBQ0Msb0JBQ1QsOERBQUNDO2dDQUVDQyxTQUFTLElBQU1WLFlBQVlRLElBQUlkLEVBQUU7Z0NBQ2pDVSxXQUFXakIsOENBQUVBLENBQ1gsMEdBQ0EsOERBQ0FZLGdCQUFnQlMsSUFBSWQsRUFBRSxHQUNsQixvQ0FDQTswQ0FHTiw0RUFBQ1M7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFBZUksSUFBSWIsSUFBSTs7Ozs7O3NEQUN0Qyw4REFBQ1E7NENBQUlDLFdBQVU7c0RBQTJDSSxJQUFJWixXQUFXOzs7Ozs7Ozs7Ozs7K0JBWnRFWSxJQUFJZCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzBCQW9CbkIsOERBQUNTO2dCQUFJQyxXQUFVOzBCQUNaSDs7Ozs7Ozs7Ozs7O0FBSVQ7S0FoRXdCSiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9UYWJTeXN0ZW0udHN4P2YyYzgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9saWIvdXRpbHMnXG5pbXBvcnQgTGVuZGluZ1JlYWxVc2VjYXNlIGZyb20gJy4vbGVuZGluZy9SZWFsVXNlY2FzZSdcbmltcG9ydCBMZW5kaW5nR2xvYmFsIGZyb20gJy4vbGVuZGluZy9HbG9iYWwnXG5pbXBvcnQgTGVuZGluZ0xMTSBmcm9tICcuL2xlbmRpbmcvTExNJ1xuaW1wb3J0IExlbmRpbmdJZGVhbCBmcm9tICcuL2xlbmRpbmcvSWRlYWwnXG5pbXBvcnQgQ29taW5nU29vbiBmcm9tICcuL0NvbWluZ1Nvb24nXG5cbmludGVyZmFjZSBUYWJTeXN0ZW1Qcm9wcyB7XG4gIHNlbGVjdGVkU2VjdG9yOiBzdHJpbmdcbiAgc2VsZWN0ZWRUYWI6IHN0cmluZ1xuICBvblRhYkNoYW5nZTogKHRhYjogc3RyaW5nKSA9PiB2b2lkXG59XG5cbmNvbnN0IHRhYnMgPSBbXG4gIHsgaWQ6ICdyZWFsLXVzZWNhc2UnLCBuYW1lOiAnUmVhbC11c2VjYXNlJywgZGVzY3JpcHRpb246ICdMaXZlIGRhdGEgYW5hbHlzaXMnIH0sXG4gIHsgaWQ6ICdnbG9iYWwnLCBuYW1lOiAnR2xvYmFsJywgZGVzY3JpcHRpb246ICdXb3JsZHdpZGUgbWV0cmljcycgfSxcbiAgeyBpZDogJ2xsbScsIG5hbWU6ICdMTE0nLCBkZXNjcmlwdGlvbjogJ0FJIGluc2lnaHRzJyB9LFxuICB7IGlkOiAnaWRlYWwnLCBuYW1lOiAnSWRlYWwgdXNlY2FzZXMnLCBkZXNjcmlwdGlvbjogJ0Jlc3QgcHJhY3RpY2VzJyB9XG5dXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFRhYlN5c3RlbSh7IHNlbGVjdGVkU2VjdG9yLCBzZWxlY3RlZFRhYiwgb25UYWJDaGFuZ2UgfTogVGFiU3lzdGVtUHJvcHMpIHtcbiAgY29uc3QgcmVuZGVyQ29udGVudCA9ICgpID0+IHtcbiAgICBpZiAoc2VsZWN0ZWRTZWN0b3IgPT09ICdsZW5kaW5nJykge1xuICAgICAgc3dpdGNoIChzZWxlY3RlZFRhYikge1xuICAgICAgICBjYXNlICdyZWFsLXVzZWNhc2UnOlxuICAgICAgICAgIHJldHVybiA8TGVuZGluZ1JlYWxVc2VjYXNlIC8+XG4gICAgICAgIGNhc2UgJ2dsb2JhbCc6XG4gICAgICAgICAgcmV0dXJuIDxMZW5kaW5nR2xvYmFsIC8+XG4gICAgICAgIGNhc2UgJ2xsbSc6XG4gICAgICAgICAgcmV0dXJuIDxMZW5kaW5nTExNIC8+XG4gICAgICAgIGNhc2UgJ2lkZWFsJzpcbiAgICAgICAgICByZXR1cm4gPExlbmRpbmdJZGVhbCAvPlxuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIHJldHVybiA8TGVuZGluZ1JlYWxVc2VjYXNlIC8+XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gRm9yIG90aGVyIHNlY3RvcnMsIHNob3cgY29taW5nIHNvb25cbiAgICByZXR1cm4gPENvbWluZ1Nvb24gc2VjdG9yPXtzZWxlY3RlZFNlY3Rvcn0gLz5cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGgtZnVsbFwiPlxuICAgICAgey8qIFRhYiBOYXZpZ2F0aW9uICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1zdXJmYWNlIGJvcmRlci1iIGJvcmRlci1ib3JkZXItY29sb3IgcHgtNCBzbTpweC02IHB5LTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBzbTp0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC10ZXh0LXByaW1hcnkgY2FwaXRhbGl6ZVwiPlxuICAgICAgICAgICAgICB7c2VsZWN0ZWRTZWN0b3J9IEFuYWx5dGljc1xuICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC10ZXh0LXNlY29uZGFyeSBoaWRkZW4gc206YmxvY2tcIj5cbiAgICAgICAgICAgICAgQ29tcHJlaGVuc2l2ZSBhbmFseXNpcyBhbmQgaW5zaWdodHMgZm9yIHtzZWxlY3RlZFNlY3Rvcn0gdXNlIGNhc2VzXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTEgYmctc3VyZmFjZS1saWdodCByb3VuZGVkLWxnIHAtMSBvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICB7dGFicy5tYXAoKHRhYikgPT4gKFxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBrZXk9e3RhYi5pZH1cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb25UYWJDaGFuZ2UodGFiLmlkKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICBcImZsZXgtMSBtaW4tdy0wIHB4LTIgc206cHgtNCBweS0zIHJvdW5kZWQtbWQgdGV4dC14cyBzbTp0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiLFxuICAgICAgICAgICAgICAgIFwiaG92ZXI6Ymctc3VyZmFjZSBob3Zlcjp0ZXh0LXRleHQtcHJpbWFyeSB3aGl0ZXNwYWNlLW5vd3JhcFwiLFxuICAgICAgICAgICAgICAgIHNlbGVjdGVkVGFiID09PSB0YWIuaWRcbiAgICAgICAgICAgICAgICAgID8gXCJiZy1wcmltYXJ5IHRleHQtd2hpdGUgc2hhZG93LXNtXCJcbiAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXRleHQtc2Vjb25kYXJ5XCJcbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57dGFiLm5hbWV9PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIG9wYWNpdHktNzUgbXQtMSBoaWRkZW4gc206YmxvY2tcIj57dGFiLmRlc2NyaXB0aW9ufTwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogVGFiIENvbnRlbnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy1hdXRvXCI+XG4gICAgICAgIHtyZW5kZXJDb250ZW50KCl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImNuIiwiTGVuZGluZ1JlYWxVc2VjYXNlIiwiTGVuZGluZ0dsb2JhbCIsIkxlbmRpbmdMTE0iLCJMZW5kaW5nSWRlYWwiLCJDb21pbmdTb29uIiwidGFicyIsImlkIiwibmFtZSIsImRlc2NyaXB0aW9uIiwiVGFiU3lzdGVtIiwic2VsZWN0ZWRTZWN0b3IiLCJzZWxlY3RlZFRhYiIsIm9uVGFiQ2hhbmdlIiwicmVuZGVyQ29udGVudCIsInNlY3RvciIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwicCIsIm1hcCIsInRhYiIsImJ1dHRvbiIsIm9uQ2xpY2siXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TabSystem.tsx\n"));

/***/ })

});