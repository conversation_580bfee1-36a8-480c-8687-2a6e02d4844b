"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_CreditCard_Gift_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,CreditCard,Gift,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_CreditCard_Gift_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,CreditCard,Gift,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_CreditCard_Gift_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,CreditCard,Gift,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_CreditCard_Gift_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,CreditCard,Gift,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_CreditCard_Gift_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,CreditCard,Gift,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst sectors = [\n    {\n        id: \"lending\",\n        name: \"Lending\",\n        icon: _barrel_optimize_names_ChevronRight_CreditCard_Gift_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        description: \"Credit risk assessment\",\n        color: \"text-chart-1\",\n        bgColor: \"bg-chart-1/10\",\n        borderColor: \"border-chart-1/20\"\n    },\n    {\n        id: \"airdrops\",\n        name: \"Airdrops\",\n        icon: _barrel_optimize_names_ChevronRight_CreditCard_Gift_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        description: \"Sybil resistance\",\n        color: \"text-chart-2\",\n        bgColor: \"bg-chart-2/10\",\n        borderColor: \"border-chart-2/20\"\n    },\n    {\n        id: \"dexes\",\n        name: \"DEXes\",\n        icon: _barrel_optimize_names_ChevronRight_CreditCard_Gift_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        description: \"Trading reputation\",\n        color: \"text-chart-3\",\n        bgColor: \"bg-chart-3/10\",\n        borderColor: \"border-chart-3/20\"\n    },\n    {\n        id: \"perps\",\n        name: \"Perps\",\n        icon: _barrel_optimize_names_ChevronRight_CreditCard_Gift_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        description: \"Leverage assessment\",\n        color: \"text-chart-4\",\n        bgColor: \"bg-chart-4/10\",\n        borderColor: \"border-chart-4/20\"\n    }\n];\nfunction Sidebar(param) {\n    let { selectedSector, onSectorChange, onClose } = param;\n    const handleSectorChange = (sector)=>{\n        onSectorChange(sector);\n        onClose === null || onClose === void 0 ? void 0 : onClose() // Close mobile sidebar when sector is selected\n        ;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-surface border-r border-border-color flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-border-color\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-text-primary mb-2\",\n                        children: \"Use Case Sectors\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-text-secondary\",\n                        children: \"Explore zScore applications across different DeFi sectors\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-4 space-y-3\",\n                children: sectors.map((sector)=>{\n                    const Icon = sector.icon;\n                    const isSelected = selectedSector === sector.id;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onSectorChange(sector.id),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-full p-4 rounded-xl border transition-all duration-200 group\", \"hover:shadow-card-hover hover:scale-[1.02]\", isSelected ? \"\".concat(sector.bgColor, \" \").concat(sector.borderColor, \" border-2 shadow-card\") : \"bg-surface-light border-border-color hover:border-primary/30\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-10 h-10 rounded-lg flex items-center justify-center\", isSelected ? sector.bgColor : \"bg-surface\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-5 h-5\", isSelected ? sector.color : \"text-text-secondary\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"font-medium\", isSelected ? \"text-text-primary\" : \"text-text-primary group-hover:text-text-primary\"),\n                                                    children: sector.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"text-xs\", isSelected ? \"text-text-secondary\" : \"text-text-muted group-hover:text-text-secondary\"),\n                                                    children: sector.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_CreditCard_Gift_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-4 h-4 transition-transform\", isSelected ? \"\".concat(sector.color, \" rotate-90\") : \"text-text-muted group-hover:text-text-secondary\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 15\n                        }, this)\n                    }, sector.id, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-t border-border-color\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-primary/10 to-primary-light/10 border border-primary/20 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-medium text-text-primary mb-1\",\n                            children: \"Need Help?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-text-secondary mb-3\",\n                            children: \"Learn more about zScore implementation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full bg-primary text-white text-sm py-2 rounded-lg hover:bg-primary-light transition-colors\",\n                            children: \"View Documentation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/Sidebar.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

});