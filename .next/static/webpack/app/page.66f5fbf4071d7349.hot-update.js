"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ComingSoon.tsx":
/*!***************************************!*\
  !*** ./src/components/ComingSoon.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ComingSoon; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Construction_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Construction,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/construction.js\");\n/* harmony import */ var _barrel_optimize_names_Construction_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Construction,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ComingSoon(param) {\n    let { sector } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center h-full bg-background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center max-w-md mx-auto p-4 sm:p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-primary to-primary-light rounded-full flex items-center justify-center mx-auto mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Construction_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        className: \"w-8 h-8 sm:w-10 sm:h-10 text-white\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl sm:text-2xl font-bold text-text-primary mb-4 capitalize\",\n                    children: [\n                        sector,\n                        \" Analytics Coming Soon\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-text-secondary mb-6 leading-relaxed text-sm sm:text-base\",\n                    children: [\n                        \"We're working hard to bring you comprehensive analytics for \",\n                        sector,\n                        \" use cases. This section will include detailed metrics, risk assessments, and insights specific to the \",\n                        sector,\n                        \" sector.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-surface border border-border-color rounded-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Construction_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-6 h-6 text-accent mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-accent font-medium\",\n                                    children: \"What's Coming\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-sm text-text-secondary space-y-2 text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: [\n                                        \"• Real-time \",\n                                        sector,\n                                        \" metrics and KPIs\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Risk assessment models\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Historical trend analysis\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Predictive insights using AI\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Best practice recommendations\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-light transition-colors\",\n                    children: \"Get Notified When Ready\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/ComingSoon.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_c = ComingSoon;\nvar _c;\n$RefreshReg$(_c, \"ComingSoon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ComingSoon.tsx\n"));

/***/ })

});