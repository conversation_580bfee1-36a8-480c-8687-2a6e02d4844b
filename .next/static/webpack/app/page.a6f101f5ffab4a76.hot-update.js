"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/lending/RealUsecase.tsx":
/*!************************************************!*\
  !*** ./src/components/lending/RealUsecase.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LendingRealUsecase; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,DollarSign,Shield,TrendingDown,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,DollarSign,Shield,TrendingDown,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,DollarSign,Shield,TrendingDown,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,DollarSign,Shield,TrendingDown,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,DollarSign,Shield,TrendingDown,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,DollarSign,Shield,TrendingDown,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _components_charts_MetricCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/charts/MetricCard */ \"(app-pages-browser)/./src/components/charts/MetricCard.tsx\");\n/* harmony import */ var _components_charts_RiskDistributionChart__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/charts/RiskDistributionChart */ \"(app-pages-browser)/./src/components/charts/RiskDistributionChart.tsx\");\n/* harmony import */ var _components_charts_ChainDistributionChart__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/charts/ChainDistributionChart */ \"(app-pages-browser)/./src/components/charts/ChainDistributionChart.tsx\");\n/* harmony import */ var _components_charts_LiquidationTrendsChart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/charts/LiquidationTrendsChart */ \"(app-pages-browser)/./src/components/charts/LiquidationTrendsChart.tsx\");\n/* harmony import */ var _components_charts_TopWalletsTable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/charts/TopWalletsTable */ \"(app-pages-browser)/./src/components/charts/TopWalletsTable.tsx\");\n/* harmony import */ var _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/liquidationData */ \"(app-pages-browser)/./src/data/liquidationData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction LendingRealUsecase() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 sm:p-6 space-y-4 sm:space-y-6 bg-background min-h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-start lg:justify-between space-y-4 lg:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-text-primary mb-2\",\n                                    children: \"DeFi Lending Creditworthiness Analysis\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-text-secondary mb-4 text-sm sm:text-base\",\n                                    children: [\n                                        \"Comprehensive analysis of DeFi wallet creditworthiness using zScore metrics, based on a study of \",\n                                        _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__.keyFindings.totalLiquidatedAddresses.toLocaleString(),\n                                        \" liquidated wallet addresses on Aave over the last 48 hours (02-02-2025 to 03-02-2025).\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-secondary rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-text-secondary\",\n                                                    children: \"Live Data\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 36,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-primary rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-text-secondary\",\n                                                    children: \"Real-time Analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-primary/10 to-primary-light/10 border border-primary/20 rounded-lg p-4 lg:ml-4 flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-8 h-8 text-primary mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-text-primary\",\n                                        children: \"Coverage\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-primary\",\n                                        children: [\n                                            _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__.keyFindings.coveragePercentage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-text-secondary\",\n                                        children: [\n                                            _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__.keyFindings.analyzedWallets.toLocaleString(),\n                                            \" wallets analyzed\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_MetricCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        title: \"Total Wallets Tracked\",\n                        value: _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__.keyFindings.totalLiquidatedAddresses.toLocaleString(),\n                        change: \"+2.4% this week\",\n                        changeType: \"positive\",\n                        icon: _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                        description: \"Liquidated addresses analyzed\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_MetricCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        title: \"Chains Integrated\",\n                        value: \"10\",\n                        change: \"+2 new this month\",\n                        changeType: \"positive\",\n                        icon: _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                        description: \"Blockchain networks covered\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_MetricCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        title: \"Protocols Supported\",\n                        value: \"25+\",\n                        change: \"Aave, Compound, MakerDAO\",\n                        changeType: \"neutral\",\n                        icon: _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                        description: \"DeFi lending protocols\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_MetricCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        title: \"Daily Transactions\",\n                        value: \"1.2M\",\n                        change: \"+10.7% increase\",\n                        changeType: \"positive\",\n                        icon: _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                        description: \"Real-time monitoring\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-4\",\n                        children: \"Research Question\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-primary/5 to-primary-light/5 border border-primary/10 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-text-primary mb-3\",\n                                children: \"Can a decentralized reputation system built on Eigenlayer be a good alibi for creditworthiness?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-text-secondary leading-relaxed\",\n                                children: [\n                                    \"This research presents a comprehensive analysis of DeFi wallet creditworthiness using zScore metrics. The findings demonstrate how zScore can serve as a reliable predictor for liquidation risk assessment in DeFi lending, with the primary risk zone (190-200) accounting for \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        className: \"text-primary\",\n                                        children: \"28.87%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 82\n                                    }, this),\n                                    \" of all liquidations.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_RiskDistributionChart__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_ChainDistributionChart__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_LiquidationTrendsChart__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6\",\n                        children: \"Key Findings\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-6 h-6 text-danger\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-text-primary\",\n                                                children: \"Primary Risk Zone\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-danger mb-2\",\n                                        children: \"190-200\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-text-secondary\",\n                                        children: [\n                                            _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__.keyFindings.primaryRiskZone.percentage,\n                                            \"% of liquidations (\",\n                                            _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__.keyFindings.primaryRiskZone.count.toLocaleString(),\n                                            \" wallets)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-text-primary\",\n                                                children: \"Average zScore\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-primary mb-2\",\n                                        children: _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__.keyFindings.averageZScore\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-text-secondary\",\n                                        children: [\n                                            \"Median: \",\n                                            _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__.keyFindings.medianZScore,\n                                            \" (right-skewed distribution)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-6 h-6 text-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-text-primary\",\n                                                children: \"High Risk Threshold\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-accent mb-2\",\n                                        children: \"<200\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-text-secondary\",\n                                        children: \"~45% of liquidations occur in this range\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_TopWalletsTable__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6\",\n                        children: \"Risk Assessment Framework\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b border-border-color\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-sm font-medium text-text-secondary\",\n                                                children: \"Risk Level\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-sm font-medium text-text-secondary\",\n                                                children: \"Score Range\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-sm font-medium text-text-secondary\",\n                                                children: \"% of Liquidations\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-sm font-medium text-text-secondary\",\n                                                children: \"Implication\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-border-color/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-danger/10 text-danger border border-danger/20\",\n                                                        children: \"Very High\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-primary\",\n                                                    children: \"<200\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-primary\",\n                                                    children: \"~45%\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-secondary\",\n                                                    children: \"Needs max collateralization\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-border-color/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-accent/10 text-accent border border-accent/20\",\n                                                        children: \"High\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-primary\",\n                                                    children: \"200–300\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-primary\",\n                                                    children: \"~30%\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-secondary\",\n                                                    children: \"Higher liquidation probability\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-border-color/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-secondary/10 text-secondary border border-secondary/20\",\n                                                        children: \"Moderate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-primary\",\n                                                    children: \"300–400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-primary\",\n                                                    children: \"~20%\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-secondary\",\n                                                    children: \"Standard collateralization\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary border border-primary/20\",\n                                                        children: \"Lower Risk\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-primary\",\n                                                    children: \"400+\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-primary\",\n                                                    children: \"~5%\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-secondary\",\n                                                    children: \"Favorable lending terms\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n_c = LendingRealUsecase;\nvar _c;\n$RefreshReg$(_c, \"LendingRealUsecase\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2xlbmRpbmcvUmVhbFVzZWNhc2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNxQjtBQUNrQztBQUNzQjtBQUNFO0FBQ0E7QUFDZDtBQUNiO0FBRXJDLFNBQVNZO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBR0QsV0FBVTs4Q0FBdUQ7Ozs7Ozs4Q0FHckUsOERBQUNFO29DQUFFRixXQUFVOzt3Q0FBZ0Q7d0NBRXRDSCw4REFBV0EsQ0FBQ00sd0JBQXdCLENBQUNDLGNBQWM7d0NBQUc7Ozs7Ozs7OENBRzdFLDhEQUFDTDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7Ozs7Ozs4REFDZiw4REFBQ0s7b0RBQUtMLFdBQVU7OERBQXNCOzs7Ozs7Ozs7Ozs7c0RBRXhDLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzs7Ozs7OERBQ2YsOERBQUNLO29EQUFLTCxXQUFVOzhEQUFzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUk1Qyw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ1Qsd0lBQU1BO3dDQUFDUyxXQUFVOzs7Ozs7a0RBQ2xCLDhEQUFDRTt3Q0FBRUYsV0FBVTtrREFBd0M7Ozs7OztrREFDckQsOERBQUNFO3dDQUFFRixXQUFVOzs0Q0FBbUNILDhEQUFXQSxDQUFDUyxrQkFBa0I7NENBQUM7Ozs7Ozs7a0RBQy9FLDhEQUFDSjt3Q0FBRUYsV0FBVTs7NENBQ1ZILDhEQUFXQSxDQUFDVSxlQUFlLENBQUNILGNBQWM7NENBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVF4RCw4REFBQ0w7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDUixxRUFBVUE7d0JBQ1RnQixPQUFNO3dCQUNOQyxPQUFPWiw4REFBV0EsQ0FBQ00sd0JBQXdCLENBQUNDLGNBQWM7d0JBQzFETSxRQUFPO3dCQUNQQyxZQUFXO3dCQUNYQyxNQUFNMUIsd0lBQUtBO3dCQUNYMkIsYUFBWTs7Ozs7O2tDQUVkLDhEQUFDckIscUVBQVVBO3dCQUNUZ0IsT0FBTTt3QkFDTkMsT0FBTTt3QkFDTkMsUUFBTzt3QkFDUEMsWUFBVzt3QkFDWEMsTUFBTXRCLHdJQUFTQTt3QkFDZnVCLGFBQVk7Ozs7OztrQ0FFZCw4REFBQ3JCLHFFQUFVQTt3QkFDVGdCLE9BQU07d0JBQ05DLE9BQU07d0JBQ05DLFFBQU87d0JBQ1BDLFlBQVc7d0JBQ1hDLE1BQU12Qix5SUFBVUE7d0JBQ2hCd0IsYUFBWTs7Ozs7O2tDQUVkLDhEQUFDckIscUVBQVVBO3dCQUNUZ0IsT0FBTTt3QkFDTkMsT0FBTTt3QkFDTkMsUUFBTzt3QkFDUEMsWUFBVzt3QkFDWEMsTUFBTXpCLHlJQUFZQTt3QkFDbEIwQixhQUFZOzs7Ozs7Ozs7Ozs7MEJBS2hCLDhEQUFDZDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNjO3dCQUFHZCxXQUFVO2tDQUErQzs7Ozs7O2tDQUM3RCw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDZTtnQ0FBR2YsV0FBVTswQ0FBNkM7Ozs7OzswQ0FHM0QsOERBQUNFO2dDQUFFRixXQUFVOztvQ0FBc0M7a0RBR29CLDhEQUFDZ0I7d0NBQU9oQixXQUFVO2tEQUFlOzs7Ozs7b0NBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTzNILDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNQLGdGQUFxQkE7Ozs7O2tDQUN0Qiw4REFBQ0MsaUZBQXNCQTs7Ozs7Ozs7Ozs7MEJBR3pCLDhEQUFDSztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0wsaUZBQXNCQTs7Ozs7Ozs7OzswQkFJekIsOERBQUNJO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ2M7d0JBQUdkLFdBQVU7a0NBQStDOzs7Ozs7a0NBQzdELDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDWix5SUFBYUE7Z0RBQUNZLFdBQVU7Ozs7OzswREFDekIsOERBQUNlO2dEQUFHZixXQUFVOzBEQUFnQzs7Ozs7Ozs7Ozs7O2tEQUVoRCw4REFBQ0U7d0NBQUVGLFdBQVU7a0RBQXNDOzs7Ozs7a0RBQ25ELDhEQUFDRTt3Q0FBRUYsV0FBVTs7NENBQ1ZILDhEQUFXQSxDQUFDb0IsZUFBZSxDQUFDQyxVQUFVOzRDQUFDOzRDQUFvQnJCLDhEQUFXQSxDQUFDb0IsZUFBZSxDQUFDRSxLQUFLLENBQUNmLGNBQWM7NENBQUc7Ozs7Ozs7Ozs7Ozs7MENBSW5ILDhEQUFDTDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ1Ysd0lBQVNBO2dEQUFDVSxXQUFVOzs7Ozs7MERBQ3JCLDhEQUFDZTtnREFBR2YsV0FBVTswREFBZ0M7Ozs7Ozs7Ozs7OztrREFFaEQsOERBQUNFO3dDQUFFRixXQUFVO2tEQUF3Q0gsOERBQVdBLENBQUN1QixhQUFhOzs7Ozs7a0RBQzlFLDhEQUFDbEI7d0NBQUVGLFdBQVU7OzRDQUE4Qjs0Q0FDaENILDhEQUFXQSxDQUFDd0IsWUFBWTs0Q0FBQzs7Ozs7Ozs7Ozs7OzswQ0FJdEMsOERBQUN0QjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ2IseUlBQVlBO2dEQUFDYSxXQUFVOzs7Ozs7MERBQ3hCLDhEQUFDZTtnREFBR2YsV0FBVTswREFBZ0M7Ozs7Ozs7Ozs7OztrREFFaEQsOERBQUNFO3dDQUFFRixXQUFVO2tEQUFzQzs7Ozs7O2tEQUNuRCw4REFBQ0U7d0NBQUVGLFdBQVU7a0RBQThCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUWpELDhEQUFDSiwwRUFBZUE7Ozs7OzBCQUdoQiw4REFBQ0c7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDYzt3QkFBR2QsV0FBVTtrQ0FBK0M7Ozs7OztrQ0FDN0QsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDc0I7NEJBQU10QixXQUFVOzs4Q0FDZiw4REFBQ3VCOzhDQUNDLDRFQUFDQzt3Q0FBR3hCLFdBQVU7OzBEQUNaLDhEQUFDeUI7Z0RBQUd6QixXQUFVOzBEQUE4RDs7Ozs7OzBEQUM1RSw4REFBQ3lCO2dEQUFHekIsV0FBVTswREFBOEQ7Ozs7OzswREFDNUUsOERBQUN5QjtnREFBR3pCLFdBQVU7MERBQThEOzs7Ozs7MERBQzVFLDhEQUFDeUI7Z0RBQUd6QixXQUFVOzBEQUE4RDs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBR2hGLDhEQUFDMEI7O3NEQUNDLDhEQUFDRjs0Q0FBR3hCLFdBQVU7OzhEQUNaLDhEQUFDMkI7b0RBQUczQixXQUFVOzhEQUNaLDRFQUFDSzt3REFBS0wsV0FBVTtrRUFBdUg7Ozs7Ozs7Ozs7OzhEQUl6SSw4REFBQzJCO29EQUFHM0IsV0FBVTs4REFBOEI7Ozs7Ozs4REFDNUMsOERBQUMyQjtvREFBRzNCLFdBQVU7OERBQThCOzs7Ozs7OERBQzVDLDhEQUFDMkI7b0RBQUczQixXQUFVOzhEQUFnQzs7Ozs7Ozs7Ozs7O3NEQUVoRCw4REFBQ3dCOzRDQUFHeEIsV0FBVTs7OERBQ1osOERBQUMyQjtvREFBRzNCLFdBQVU7OERBQ1osNEVBQUNLO3dEQUFLTCxXQUFVO2tFQUF1SDs7Ozs7Ozs7Ozs7OERBSXpJLDhEQUFDMkI7b0RBQUczQixXQUFVOzhEQUE4Qjs7Ozs7OzhEQUM1Qyw4REFBQzJCO29EQUFHM0IsV0FBVTs4REFBOEI7Ozs7Ozs4REFDNUMsOERBQUMyQjtvREFBRzNCLFdBQVU7OERBQWdDOzs7Ozs7Ozs7Ozs7c0RBRWhELDhEQUFDd0I7NENBQUd4QixXQUFVOzs4REFDWiw4REFBQzJCO29EQUFHM0IsV0FBVTs4REFDWiw0RUFBQ0s7d0RBQUtMLFdBQVU7a0VBQWdJOzs7Ozs7Ozs7Ozs4REFJbEosOERBQUMyQjtvREFBRzNCLFdBQVU7OERBQThCOzs7Ozs7OERBQzVDLDhEQUFDMkI7b0RBQUczQixXQUFVOzhEQUE4Qjs7Ozs7OzhEQUM1Qyw4REFBQzJCO29EQUFHM0IsV0FBVTs4REFBZ0M7Ozs7Ozs7Ozs7OztzREFFaEQsOERBQUN3Qjs7OERBQ0MsOERBQUNHO29EQUFHM0IsV0FBVTs4REFDWiw0RUFBQ0s7d0RBQUtMLFdBQVU7a0VBQTBIOzs7Ozs7Ozs7Ozs4REFJNUksOERBQUMyQjtvREFBRzNCLFdBQVU7OERBQThCOzs7Ozs7OERBQzVDLDhEQUFDMkI7b0RBQUczQixXQUFVOzhEQUE4Qjs7Ozs7OzhEQUM1Qyw4REFBQzJCO29EQUFHM0IsV0FBVTs4REFBZ0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUTlEO0tBM013QkYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvbGVuZGluZy9SZWFsVXNlY2FzZS50c3g/NmE4NSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHtcbiAgVXNlcnMsXG4gIFRyZW5kaW5nRG93bixcbiAgQWxlcnRUcmlhbmdsZSxcbiAgRG9sbGFyU2lnbixcbiAgQmFyQ2hhcnQzLFxuICBTaGllbGRcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IE1ldHJpY0NhcmQgZnJvbSAnQC9jb21wb25lbnRzL2NoYXJ0cy9NZXRyaWNDYXJkJ1xuaW1wb3J0IFJpc2tEaXN0cmlidXRpb25DaGFydCBmcm9tICdAL2NvbXBvbmVudHMvY2hhcnRzL1Jpc2tEaXN0cmlidXRpb25DaGFydCdcbmltcG9ydCBDaGFpbkRpc3RyaWJ1dGlvbkNoYXJ0IGZyb20gJ0AvY29tcG9uZW50cy9jaGFydHMvQ2hhaW5EaXN0cmlidXRpb25DaGFydCdcbmltcG9ydCBMaXF1aWRhdGlvblRyZW5kc0NoYXJ0IGZyb20gJ0AvY29tcG9uZW50cy9jaGFydHMvTGlxdWlkYXRpb25UcmVuZHNDaGFydCdcbmltcG9ydCBUb3BXYWxsZXRzVGFibGUgZnJvbSAnQC9jb21wb25lbnRzL2NoYXJ0cy9Ub3BXYWxsZXRzVGFibGUnXG5pbXBvcnQgeyBrZXlGaW5kaW5ncyB9IGZyb20gJ0AvZGF0YS9saXF1aWRhdGlvbkRhdGEnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExlbmRpbmdSZWFsVXNlY2FzZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBzbTpwLTYgc3BhY2UteS00IHNtOnNwYWNlLXktNiBiZy1iYWNrZ3JvdW5kIG1pbi1oLWZ1bGxcIj5cbiAgICAgIHsvKiBIZWFkZXIgU2VjdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc3VyZmFjZSBib3JkZXIgYm9yZGVyLWJvcmRlci1jb2xvciByb3VuZGVkLXhsIHAtNCBzbTpwLTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGxnOmZsZXgtcm93IGxnOml0ZW1zLXN0YXJ0IGxnOmp1c3RpZnktYmV0d2VlbiBzcGFjZS15LTQgbGc6c3BhY2UteS0wXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIHNtOnRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXRleHQtcHJpbWFyeSBtYi0yXCI+XG4gICAgICAgICAgICAgIERlRmkgTGVuZGluZyBDcmVkaXR3b3J0aGluZXNzIEFuYWx5c2lzXG4gICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC10ZXh0LXNlY29uZGFyeSBtYi00IHRleHQtc20gc206dGV4dC1iYXNlXCI+XG4gICAgICAgICAgICAgIENvbXByZWhlbnNpdmUgYW5hbHlzaXMgb2YgRGVGaSB3YWxsZXQgY3JlZGl0d29ydGhpbmVzcyB1c2luZyB6U2NvcmUgbWV0cmljcyxcbiAgICAgICAgICAgICAgYmFzZWQgb24gYSBzdHVkeSBvZiB7a2V5RmluZGluZ3MudG90YWxMaXF1aWRhdGVkQWRkcmVzc2VzLnRvTG9jYWxlU3RyaW5nKCl9IGxpcXVpZGF0ZWQgd2FsbGV0IGFkZHJlc3Nlc1xuICAgICAgICAgICAgICBvbiBBYXZlIG92ZXIgdGhlIGxhc3QgNDggaG91cnMgKDAyLTAyLTIwMjUgdG8gMDMtMDItMjAyNSkuXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgc206aXRlbXMtY2VudGVyIHNtOnNwYWNlLXgtNCBzcGFjZS15LTIgc206c3BhY2UteS0wIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctc2Vjb25kYXJ5IHJvdW5kZWQtZnVsbFwiIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC10ZXh0LXNlY29uZGFyeVwiPkxpdmUgRGF0YTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLXByaW1hcnkgcm91bmRlZC1mdWxsXCIgLz5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXRleHQtc2Vjb25kYXJ5XCI+UmVhbC10aW1lIEFuYWx5c2lzPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLXByaW1hcnkvMTAgdG8tcHJpbWFyeS1saWdodC8xMCBib3JkZXIgYm9yZGVyLXByaW1hcnkvMjAgcm91bmRlZC1sZyBwLTQgbGc6bWwtNCBmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxTaGllbGQgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LXByaW1hcnkgbXgtYXV0byBtYi0yXCIgLz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXRleHQtcHJpbWFyeVwiPkNvdmVyYWdlPC9wPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wcmltYXJ5XCI+e2tleUZpbmRpbmdzLmNvdmVyYWdlUGVyY2VudGFnZX0lPC9wPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtdGV4dC1zZWNvbmRhcnlcIj5cbiAgICAgICAgICAgICAgICB7a2V5RmluZGluZ3MuYW5hbHl6ZWRXYWxsZXRzLnRvTG9jYWxlU3RyaW5nKCl9IHdhbGxldHMgYW5hbHl6ZWRcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBLZXkgTWV0cmljcyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNlwiPlxuICAgICAgICA8TWV0cmljQ2FyZFxuICAgICAgICAgIHRpdGxlPVwiVG90YWwgV2FsbGV0cyBUcmFja2VkXCJcbiAgICAgICAgICB2YWx1ZT17a2V5RmluZGluZ3MudG90YWxMaXF1aWRhdGVkQWRkcmVzc2VzLnRvTG9jYWxlU3RyaW5nKCl9XG4gICAgICAgICAgY2hhbmdlPVwiKzIuNCUgdGhpcyB3ZWVrXCJcbiAgICAgICAgICBjaGFuZ2VUeXBlPVwicG9zaXRpdmVcIlxuICAgICAgICAgIGljb249e1VzZXJzfVxuICAgICAgICAgIGRlc2NyaXB0aW9uPVwiTGlxdWlkYXRlZCBhZGRyZXNzZXMgYW5hbHl6ZWRcIlxuICAgICAgICAvPlxuICAgICAgICA8TWV0cmljQ2FyZFxuICAgICAgICAgIHRpdGxlPVwiQ2hhaW5zIEludGVncmF0ZWRcIlxuICAgICAgICAgIHZhbHVlPVwiMTBcIlxuICAgICAgICAgIGNoYW5nZT1cIisyIG5ldyB0aGlzIG1vbnRoXCJcbiAgICAgICAgICBjaGFuZ2VUeXBlPVwicG9zaXRpdmVcIlxuICAgICAgICAgIGljb249e0JhckNoYXJ0M31cbiAgICAgICAgICBkZXNjcmlwdGlvbj1cIkJsb2NrY2hhaW4gbmV0d29ya3MgY292ZXJlZFwiXG4gICAgICAgIC8+XG4gICAgICAgIDxNZXRyaWNDYXJkXG4gICAgICAgICAgdGl0bGU9XCJQcm90b2NvbHMgU3VwcG9ydGVkXCJcbiAgICAgICAgICB2YWx1ZT1cIjI1K1wiXG4gICAgICAgICAgY2hhbmdlPVwiQWF2ZSwgQ29tcG91bmQsIE1ha2VyREFPXCJcbiAgICAgICAgICBjaGFuZ2VUeXBlPVwibmV1dHJhbFwiXG4gICAgICAgICAgaWNvbj17RG9sbGFyU2lnbn1cbiAgICAgICAgICBkZXNjcmlwdGlvbj1cIkRlRmkgbGVuZGluZyBwcm90b2NvbHNcIlxuICAgICAgICAvPlxuICAgICAgICA8TWV0cmljQ2FyZFxuICAgICAgICAgIHRpdGxlPVwiRGFpbHkgVHJhbnNhY3Rpb25zXCJcbiAgICAgICAgICB2YWx1ZT1cIjEuMk1cIlxuICAgICAgICAgIGNoYW5nZT1cIisxMC43JSBpbmNyZWFzZVwiXG4gICAgICAgICAgY2hhbmdlVHlwZT1cInBvc2l0aXZlXCJcbiAgICAgICAgICBpY29uPXtUcmVuZGluZ0Rvd259XG4gICAgICAgICAgZGVzY3JpcHRpb249XCJSZWFsLXRpbWUgbW9uaXRvcmluZ1wiXG4gICAgICAgIC8+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFJlc2VhcmNoIE92ZXJ2aWV3ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1zdXJmYWNlIGJvcmRlciBib3JkZXItYm9yZGVyLWNvbG9yIHJvdW5kZWQteGwgcC02XCI+XG4gICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC10ZXh0LXByaW1hcnkgbWItNFwiPlJlc2VhcmNoIFF1ZXN0aW9uPC9oMj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tcHJpbWFyeS81IHRvLXByaW1hcnktbGlnaHQvNSBib3JkZXIgYm9yZGVyLXByaW1hcnkvMTAgcm91bmRlZC1sZyBwLTZcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LXRleHQtcHJpbWFyeSBtYi0zXCI+XG4gICAgICAgICAgICBDYW4gYSBkZWNlbnRyYWxpemVkIHJlcHV0YXRpb24gc3lzdGVtIGJ1aWx0IG9uIEVpZ2VubGF5ZXIgYmUgYSBnb29kIGFsaWJpIGZvciBjcmVkaXR3b3J0aGluZXNzP1xuICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC10ZXh0LXNlY29uZGFyeSBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgIFRoaXMgcmVzZWFyY2ggcHJlc2VudHMgYSBjb21wcmVoZW5zaXZlIGFuYWx5c2lzIG9mIERlRmkgd2FsbGV0IGNyZWRpdHdvcnRoaW5lc3MgdXNpbmcgelNjb3JlIG1ldHJpY3MuXG4gICAgICAgICAgICBUaGUgZmluZGluZ3MgZGVtb25zdHJhdGUgaG93IHpTY29yZSBjYW4gc2VydmUgYXMgYSByZWxpYWJsZSBwcmVkaWN0b3IgZm9yIGxpcXVpZGF0aW9uIHJpc2sgYXNzZXNzbWVudFxuICAgICAgICAgICAgaW4gRGVGaSBsZW5kaW5nLCB3aXRoIHRoZSBwcmltYXJ5IHJpc2sgem9uZSAoMTkwLTIwMCkgYWNjb3VudGluZyBmb3IgPHN0cm9uZyBjbGFzc05hbWU9XCJ0ZXh0LXByaW1hcnlcIj4yOC44NyU8L3N0cm9uZz4gb2ZcbiAgICAgICAgICAgIGFsbCBsaXF1aWRhdGlvbnMuXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQ2hhcnRzIEdyaWQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgPFJpc2tEaXN0cmlidXRpb25DaGFydCAvPlxuICAgICAgICA8Q2hhaW5EaXN0cmlidXRpb25DaGFydCAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBnYXAtNlwiPlxuICAgICAgICA8TGlxdWlkYXRpb25UcmVuZHNDaGFydCAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBLZXkgRmluZGluZ3MgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXN1cmZhY2UgYm9yZGVyIGJvcmRlci1ib3JkZXItY29sb3Igcm91bmRlZC14bCBwLTZcIj5cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXRleHQtcHJpbWFyeSBtYi02XCI+S2V5IEZpbmRpbmdzPC9oMj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1zdXJmYWNlLWxpZ2h0IHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBtYi0zXCI+XG4gICAgICAgICAgICAgIDxBbGVydFRyaWFuZ2xlIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1kYW5nZXJcIiAvPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC10ZXh0LXByaW1hcnlcIj5QcmltYXJ5IFJpc2sgWm9uZTwvaDM+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWRhbmdlciBtYi0yXCI+MTkwLTIwMDwvcD5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC10ZXh0LXNlY29uZGFyeVwiPlxuICAgICAgICAgICAgICB7a2V5RmluZGluZ3MucHJpbWFyeVJpc2tab25lLnBlcmNlbnRhZ2V9JSBvZiBsaXF1aWRhdGlvbnMgKHtrZXlGaW5kaW5ncy5wcmltYXJ5Umlza1pvbmUuY291bnQudG9Mb2NhbGVTdHJpbmcoKX0gd2FsbGV0cylcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc3VyZmFjZS1saWdodCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgbWItM1wiPlxuICAgICAgICAgICAgICA8QmFyQ2hhcnQzIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1wcmltYXJ5XCIgLz5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtdGV4dC1wcmltYXJ5XCI+QXZlcmFnZSB6U2NvcmU8L2gzPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wcmltYXJ5IG1iLTJcIj57a2V5RmluZGluZ3MuYXZlcmFnZVpTY29yZX08L3A+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtdGV4dC1zZWNvbmRhcnlcIj5cbiAgICAgICAgICAgICAgTWVkaWFuOiB7a2V5RmluZGluZ3MubWVkaWFuWlNjb3JlfSAocmlnaHQtc2tld2VkIGRpc3RyaWJ1dGlvbilcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc3VyZmFjZS1saWdodCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgbWItM1wiPlxuICAgICAgICAgICAgICA8VHJlbmRpbmdEb3duIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1hY2NlbnRcIiAvPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC10ZXh0LXByaW1hcnlcIj5IaWdoIFJpc2sgVGhyZXNob2xkPC9oMz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtYWNjZW50IG1iLTJcIj4mbHQ7MjAwPC9wPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXRleHQtc2Vjb25kYXJ5XCI+XG4gICAgICAgICAgICAgIH40NSUgb2YgbGlxdWlkYXRpb25zIG9jY3VyIGluIHRoaXMgcmFuZ2VcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFRvcCBXYWxsZXRzIFRhYmxlICovfVxuICAgICAgPFRvcFdhbGxldHNUYWJsZSAvPlxuXG4gICAgICB7LyogUmlzayBBc3Nlc3NtZW50IEZyYW1ld29yayAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc3VyZmFjZSBib3JkZXIgYm9yZGVyLWJvcmRlci1jb2xvciByb3VuZGVkLXhsIHAtNlwiPlxuICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtdGV4dC1wcmltYXJ5IG1iLTZcIj5SaXNrIEFzc2Vzc21lbnQgRnJhbWV3b3JrPC9oMj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwidy1mdWxsXCI+XG4gICAgICAgICAgICA8dGhlYWQ+XG4gICAgICAgICAgICAgIDx0ciBjbGFzc05hbWU9XCJib3JkZXItYiBib3JkZXItYm9yZGVyLWNvbG9yXCI+XG4gICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtbGVmdCBweS0zIHB4LTQgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXRleHQtc2Vjb25kYXJ5XCI+UmlzayBMZXZlbDwvdGg+XG4gICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtbGVmdCBweS0zIHB4LTQgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXRleHQtc2Vjb25kYXJ5XCI+U2NvcmUgUmFuZ2U8L3RoPlxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LWxlZnQgcHktMyBweC00IHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC10ZXh0LXNlY29uZGFyeVwiPiUgb2YgTGlxdWlkYXRpb25zPC90aD5cbiAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGV4dC1sZWZ0IHB5LTMgcHgtNCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtdGV4dC1zZWNvbmRhcnlcIj5JbXBsaWNhdGlvbjwvdGg+XG4gICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICA8L3RoZWFkPlxuICAgICAgICAgICAgPHRib2R5PlxuICAgICAgICAgICAgICA8dHIgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLWJvcmRlci1jb2xvci81MFwiPlxuICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0zIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gYmctZGFuZ2VyLzEwIHRleHQtZGFuZ2VyIGJvcmRlciBib3JkZXItZGFuZ2VyLzIwXCI+XG4gICAgICAgICAgICAgICAgICAgIFZlcnkgSGlnaFxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNCB0ZXh0LXRleHQtcHJpbWFyeVwiPiZsdDsyMDA8L3RkPlxuICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTQgdGV4dC10ZXh0LXByaW1hcnlcIj5+NDUlPC90ZD5cbiAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00IHRleHQtdGV4dC1zZWNvbmRhcnlcIj5OZWVkcyBtYXggY29sbGF0ZXJhbGl6YXRpb248L3RkPlxuICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICA8dHIgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLWJvcmRlci1jb2xvci81MFwiPlxuICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0zIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gYmctYWNjZW50LzEwIHRleHQtYWNjZW50IGJvcmRlciBib3JkZXItYWNjZW50LzIwXCI+XG4gICAgICAgICAgICAgICAgICAgIEhpZ2hcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTQgdGV4dC10ZXh0LXByaW1hcnlcIj4yMDDigJMzMDA8L3RkPlxuICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTQgdGV4dC10ZXh0LXByaW1hcnlcIj5+MzAlPC90ZD5cbiAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00IHRleHQtdGV4dC1zZWNvbmRhcnlcIj5IaWdoZXIgbGlxdWlkYXRpb24gcHJvYmFiaWxpdHk8L3RkPlxuICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICA8dHIgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLWJvcmRlci1jb2xvci81MFwiPlxuICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0zIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gYmctc2Vjb25kYXJ5LzEwIHRleHQtc2Vjb25kYXJ5IGJvcmRlciBib3JkZXItc2Vjb25kYXJ5LzIwXCI+XG4gICAgICAgICAgICAgICAgICAgIE1vZGVyYXRlXG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00IHRleHQtdGV4dC1wcmltYXJ5XCI+MzAw4oCTNDAwPC90ZD5cbiAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00IHRleHQtdGV4dC1wcmltYXJ5XCI+fjIwJTwvdGQ+XG4gICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNCB0ZXh0LXRleHQtc2Vjb25kYXJ5XCI+U3RhbmRhcmQgY29sbGF0ZXJhbGl6YXRpb248L3RkPlxuICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICA8dHI+XG4gICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNFwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSBiZy1wcmltYXJ5LzEwIHRleHQtcHJpbWFyeSBib3JkZXIgYm9yZGVyLXByaW1hcnkvMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgTG93ZXIgUmlza1xuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNCB0ZXh0LXRleHQtcHJpbWFyeVwiPjQwMCs8L3RkPlxuICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTQgdGV4dC10ZXh0LXByaW1hcnlcIj5+NSU8L3RkPlxuICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTQgdGV4dC10ZXh0LXNlY29uZGFyeVwiPkZhdm9yYWJsZSBsZW5kaW5nIHRlcm1zPC90ZD5cbiAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgICAgPC90YWJsZT5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlVzZXJzIiwiVHJlbmRpbmdEb3duIiwiQWxlcnRUcmlhbmdsZSIsIkRvbGxhclNpZ24iLCJCYXJDaGFydDMiLCJTaGllbGQiLCJNZXRyaWNDYXJkIiwiUmlza0Rpc3RyaWJ1dGlvbkNoYXJ0IiwiQ2hhaW5EaXN0cmlidXRpb25DaGFydCIsIkxpcXVpZGF0aW9uVHJlbmRzQ2hhcnQiLCJUb3BXYWxsZXRzVGFibGUiLCJrZXlGaW5kaW5ncyIsIkxlbmRpbmdSZWFsVXNlY2FzZSIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwicCIsInRvdGFsTGlxdWlkYXRlZEFkZHJlc3NlcyIsInRvTG9jYWxlU3RyaW5nIiwic3BhbiIsImNvdmVyYWdlUGVyY2VudGFnZSIsImFuYWx5emVkV2FsbGV0cyIsInRpdGxlIiwidmFsdWUiLCJjaGFuZ2UiLCJjaGFuZ2VUeXBlIiwiaWNvbiIsImRlc2NyaXB0aW9uIiwiaDIiLCJoMyIsInN0cm9uZyIsInByaW1hcnlSaXNrWm9uZSIsInBlcmNlbnRhZ2UiLCJjb3VudCIsImF2ZXJhZ2VaU2NvcmUiLCJtZWRpYW5aU2NvcmUiLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJ0aCIsInRib2R5IiwidGQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/lending/RealUsecase.tsx\n"));

/***/ })

});