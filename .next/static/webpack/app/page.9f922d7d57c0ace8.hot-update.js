"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/lending/RealUsecase.tsx":
/*!************************************************!*\
  !*** ./src/components/lending/RealUsecase.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LendingRealUsecase; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,DollarSign,Shield,TrendingDown,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,DollarSign,Shield,TrendingDown,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,DollarSign,Shield,TrendingDown,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,DollarSign,Shield,TrendingDown,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,DollarSign,Shield,TrendingDown,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,DollarSign,Shield,TrendingDown,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _components_charts_MetricCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/charts/MetricCard */ \"(app-pages-browser)/./src/components/charts/MetricCard.tsx\");\n/* harmony import */ var _components_charts_RiskDistributionChart__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/charts/RiskDistributionChart */ \"(app-pages-browser)/./src/components/charts/RiskDistributionChart.tsx\");\n/* harmony import */ var _components_charts_ChainDistributionChart__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/charts/ChainDistributionChart */ \"(app-pages-browser)/./src/components/charts/ChainDistributionChart.tsx\");\n/* harmony import */ var _components_charts_LiquidationTrendsChart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/charts/LiquidationTrendsChart */ \"(app-pages-browser)/./src/components/charts/LiquidationTrendsChart.tsx\");\n/* harmony import */ var _components_charts_TopWalletsTable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/charts/TopWalletsTable */ \"(app-pages-browser)/./src/components/charts/TopWalletsTable.tsx\");\n/* harmony import */ var _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/liquidationData */ \"(app-pages-browser)/./src/data/liquidationData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction LendingRealUsecase() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 sm:p-6 space-y-4 sm:space-y-6 bg-background min-h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-start lg:justify-between space-y-4 lg:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-text-primary mb-2\",\n                                    children: \"DeFi Lending Creditworthiness Analysis\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-text-secondary mb-4 text-sm sm:text-base\",\n                                    children: [\n                                        \"Comprehensive analysis of DeFi wallet creditworthiness using zScore metrics, based on a study of \",\n                                        _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__.keyFindings.totalLiquidatedAddresses.toLocaleString(),\n                                        \" liquidated wallet addresses on Aave over the last 48 hours (02-02-2025 to 03-02-2025).\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-secondary rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-text-secondary\",\n                                                    children: \"Live Data\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 36,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-primary rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-text-secondary\",\n                                                    children: \"Real-time Analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-primary/10 to-primary-light/10 border border-primary/20 rounded-lg p-4 lg:ml-4 flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-8 h-8 text-primary mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-text-primary\",\n                                        children: \"Coverage\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-primary\",\n                                        children: [\n                                            _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__.keyFindings.coveragePercentage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-text-secondary\",\n                                        children: [\n                                            _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__.keyFindings.analyzedWallets.toLocaleString(),\n                                            \" wallets analyzed\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_MetricCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        title: \"Total Wallets Tracked\",\n                        value: _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__.keyFindings.totalLiquidatedAddresses.toLocaleString(),\n                        change: \"+2.4% this week\",\n                        changeType: \"positive\",\n                        icon: _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                        description: \"Liquidated addresses analyzed\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_MetricCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        title: \"Chains Integrated\",\n                        value: \"10\",\n                        change: \"+2 new this month\",\n                        changeType: \"positive\",\n                        icon: _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                        description: \"Blockchain networks covered\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_MetricCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        title: \"Protocols Supported\",\n                        value: \"25+\",\n                        change: \"Aave, Compound, MakerDAO\",\n                        changeType: \"neutral\",\n                        icon: _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                        description: \"DeFi lending protocols\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_MetricCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        title: \"Daily Transactions\",\n                        value: \"1.2M\",\n                        change: \"+10.7% increase\",\n                        changeType: \"positive\",\n                        icon: _barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                        description: \"Real-time monitoring\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-4\",\n                        children: \"Research Question\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-primary/5 to-primary-light/5 border border-primary/10 rounded-lg p-4 sm:p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-base sm:text-lg font-medium text-text-primary mb-3\",\n                                children: \"Can a decentralized reputation system built on Eigenlayer be a good alibi for creditworthiness?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-text-secondary leading-relaxed text-sm sm:text-base\",\n                                children: [\n                                    \"This research presents a comprehensive analysis of DeFi wallet creditworthiness using zScore metrics. The findings demonstrate how zScore can serve as a reliable predictor for liquidation risk assessment in DeFi lending, with the primary risk zone (190-200) accounting for \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        className: \"text-primary\",\n                                        children: \"28.87%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 82\n                                    }, this),\n                                    \" of all liquidations.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_RiskDistributionChart__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_ChainDistributionChart__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_LiquidationTrendsChart__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6\",\n                        children: \"Key Findings\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-6 h-6 text-danger\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-text-primary\",\n                                                children: \"Primary Risk Zone\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-danger mb-2\",\n                                        children: \"190-200\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-text-secondary\",\n                                        children: [\n                                            _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__.keyFindings.primaryRiskZone.percentage,\n                                            \"% of liquidations (\",\n                                            _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__.keyFindings.primaryRiskZone.count.toLocaleString(),\n                                            \" wallets)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-text-primary\",\n                                                children: \"Average zScore\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-primary mb-2\",\n                                        children: _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__.keyFindings.averageZScore\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-text-secondary\",\n                                        children: [\n                                            \"Median: \",\n                                            _data_liquidationData__WEBPACK_IMPORTED_MODULE_6__.keyFindings.medianZScore,\n                                            \" (right-skewed distribution)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_DollarSign_Shield_TrendingDown_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-6 h-6 text-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-text-primary\",\n                                                children: \"High Risk Threshold\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-accent mb-2\",\n                                        children: \"<200\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-text-secondary\",\n                                        children: \"~45% of liquidations occur in this range\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_TopWalletsTable__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6\",\n                        children: \"Risk Assessment Framework\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b border-border-color\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-sm font-medium text-text-secondary\",\n                                                children: \"Risk Level\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-sm font-medium text-text-secondary\",\n                                                children: \"Score Range\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-sm font-medium text-text-secondary\",\n                                                children: \"% of Liquidations\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-sm font-medium text-text-secondary\",\n                                                children: \"Implication\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-border-color/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-danger/10 text-danger border border-danger/20\",\n                                                        children: \"Very High\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-primary\",\n                                                    children: \"<200\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-primary\",\n                                                    children: \"~45%\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-secondary\",\n                                                    children: \"Needs max collateralization\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-border-color/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-accent/10 text-accent border border-accent/20\",\n                                                        children: \"High\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-primary\",\n                                                    children: \"200–300\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-primary\",\n                                                    children: \"~30%\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-secondary\",\n                                                    children: \"Higher liquidation probability\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-border-color/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-secondary/10 text-secondary border border-secondary/20\",\n                                                        children: \"Moderate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-primary\",\n                                                    children: \"300–400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-primary\",\n                                                    children: \"~20%\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-secondary\",\n                                                    children: \"Standard collateralization\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary border border-primary/20\",\n                                                        children: \"Lower Risk\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-primary\",\n                                                    children: \"400+\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-primary\",\n                                                    children: \"~5%\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-text-secondary\",\n                                                    children: \"Favorable lending terms\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/RealUsecase.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n_c = LendingRealUsecase;\nvar _c;\n$RefreshReg$(_c, \"LendingRealUsecase\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2xlbmRpbmcvUmVhbFVzZWNhc2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNxQjtBQUNrQztBQUNzQjtBQUNFO0FBQ0E7QUFDZDtBQUNiO0FBRXJDLFNBQVNZO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBR0QsV0FBVTs4Q0FBdUQ7Ozs7Ozs4Q0FHckUsOERBQUNFO29DQUFFRixXQUFVOzt3Q0FBZ0Q7d0NBRXRDSCw4REFBV0EsQ0FBQ00sd0JBQXdCLENBQUNDLGNBQWM7d0NBQUc7Ozs7Ozs7OENBRzdFLDhEQUFDTDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7Ozs7Ozs4REFDZiw4REFBQ0s7b0RBQUtMLFdBQVU7OERBQXNCOzs7Ozs7Ozs7Ozs7c0RBRXhDLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzs7Ozs7OERBQ2YsOERBQUNLO29EQUFLTCxXQUFVOzhEQUFzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUk1Qyw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ1Qsd0lBQU1BO3dDQUFDUyxXQUFVOzs7Ozs7a0RBQ2xCLDhEQUFDRTt3Q0FBRUYsV0FBVTtrREFBd0M7Ozs7OztrREFDckQsOERBQUNFO3dDQUFFRixXQUFVOzs0Q0FBbUNILDhEQUFXQSxDQUFDUyxrQkFBa0I7NENBQUM7Ozs7Ozs7a0RBQy9FLDhEQUFDSjt3Q0FBRUYsV0FBVTs7NENBQ1ZILDhEQUFXQSxDQUFDVSxlQUFlLENBQUNILGNBQWM7NENBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVF4RCw4REFBQ0w7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDUixxRUFBVUE7d0JBQ1RnQixPQUFNO3dCQUNOQyxPQUFPWiw4REFBV0EsQ0FBQ00sd0JBQXdCLENBQUNDLGNBQWM7d0JBQzFETSxRQUFPO3dCQUNQQyxZQUFXO3dCQUNYQyxNQUFNMUIsd0lBQUtBO3dCQUNYMkIsYUFBWTs7Ozs7O2tDQUVkLDhEQUFDckIscUVBQVVBO3dCQUNUZ0IsT0FBTTt3QkFDTkMsT0FBTTt3QkFDTkMsUUFBTzt3QkFDUEMsWUFBVzt3QkFDWEMsTUFBTXRCLHdJQUFTQTt3QkFDZnVCLGFBQVk7Ozs7OztrQ0FFZCw4REFBQ3JCLHFFQUFVQTt3QkFDVGdCLE9BQU07d0JBQ05DLE9BQU07d0JBQ05DLFFBQU87d0JBQ1BDLFlBQVc7d0JBQ1hDLE1BQU12Qix5SUFBVUE7d0JBQ2hCd0IsYUFBWTs7Ozs7O2tDQUVkLDhEQUFDckIscUVBQVVBO3dCQUNUZ0IsT0FBTTt3QkFDTkMsT0FBTTt3QkFDTkMsUUFBTzt3QkFDUEMsWUFBVzt3QkFDWEMsTUFBTXpCLHlJQUFZQTt3QkFDbEIwQixhQUFZOzs7Ozs7Ozs7Ozs7MEJBS2hCLDhEQUFDZDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNjO3dCQUFHZCxXQUFVO2tDQUErQzs7Ozs7O2tDQUM3RCw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDZTtnQ0FBR2YsV0FBVTswQ0FBMEQ7Ozs7OzswQ0FHeEUsOERBQUNFO2dDQUFFRixXQUFVOztvQ0FBMkQ7a0RBR0QsOERBQUNnQjt3Q0FBT2hCLFdBQVU7a0RBQWU7Ozs7OztvQ0FBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPM0gsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ1AsZ0ZBQXFCQTs7Ozs7a0NBQ3RCLDhEQUFDQyxpRkFBc0JBOzs7Ozs7Ozs7OzswQkFHekIsOERBQUNLO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDTCxpRkFBc0JBOzs7Ozs7Ozs7OzBCQUl6Qiw4REFBQ0k7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDYzt3QkFBR2QsV0FBVTtrQ0FBK0M7Ozs7OztrQ0FDN0QsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNaLHlJQUFhQTtnREFBQ1ksV0FBVTs7Ozs7OzBEQUN6Qiw4REFBQ2U7Z0RBQUdmLFdBQVU7MERBQWdDOzs7Ozs7Ozs7Ozs7a0RBRWhELDhEQUFDRTt3Q0FBRUYsV0FBVTtrREFBc0M7Ozs7OztrREFDbkQsOERBQUNFO3dDQUFFRixXQUFVOzs0Q0FDVkgsOERBQVdBLENBQUNvQixlQUFlLENBQUNDLFVBQVU7NENBQUM7NENBQW9CckIsOERBQVdBLENBQUNvQixlQUFlLENBQUNFLEtBQUssQ0FBQ2YsY0FBYzs0Q0FBRzs7Ozs7Ozs7Ozs7OzswQ0FJbkgsOERBQUNMO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDVix3SUFBU0E7Z0RBQUNVLFdBQVU7Ozs7OzswREFDckIsOERBQUNlO2dEQUFHZixXQUFVOzBEQUFnQzs7Ozs7Ozs7Ozs7O2tEQUVoRCw4REFBQ0U7d0NBQUVGLFdBQVU7a0RBQXdDSCw4REFBV0EsQ0FBQ3VCLGFBQWE7Ozs7OztrREFDOUUsOERBQUNsQjt3Q0FBRUYsV0FBVTs7NENBQThCOzRDQUNoQ0gsOERBQVdBLENBQUN3QixZQUFZOzRDQUFDOzs7Ozs7Ozs7Ozs7OzBDQUl0Qyw4REFBQ3RCO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDYix5SUFBWUE7Z0RBQUNhLFdBQVU7Ozs7OzswREFDeEIsOERBQUNlO2dEQUFHZixXQUFVOzBEQUFnQzs7Ozs7Ozs7Ozs7O2tEQUVoRCw4REFBQ0U7d0NBQUVGLFdBQVU7a0RBQXNDOzs7Ozs7a0RBQ25ELDhEQUFDRTt3Q0FBRUYsV0FBVTtrREFBOEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRakQsOERBQUNKLDBFQUFlQTs7Ozs7MEJBR2hCLDhEQUFDRztnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNjO3dCQUFHZCxXQUFVO2tDQUErQzs7Ozs7O2tDQUM3RCw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNzQjs0QkFBTXRCLFdBQVU7OzhDQUNmLDhEQUFDdUI7OENBQ0MsNEVBQUNDO3dDQUFHeEIsV0FBVTs7MERBQ1osOERBQUN5QjtnREFBR3pCLFdBQVU7MERBQThEOzs7Ozs7MERBQzVFLDhEQUFDeUI7Z0RBQUd6QixXQUFVOzBEQUE4RDs7Ozs7OzBEQUM1RSw4REFBQ3lCO2dEQUFHekIsV0FBVTswREFBOEQ7Ozs7OzswREFDNUUsOERBQUN5QjtnREFBR3pCLFdBQVU7MERBQThEOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FHaEYsOERBQUMwQjs7c0RBQ0MsOERBQUNGOzRDQUFHeEIsV0FBVTs7OERBQ1osOERBQUMyQjtvREFBRzNCLFdBQVU7OERBQ1osNEVBQUNLO3dEQUFLTCxXQUFVO2tFQUF1SDs7Ozs7Ozs7Ozs7OERBSXpJLDhEQUFDMkI7b0RBQUczQixXQUFVOzhEQUE4Qjs7Ozs7OzhEQUM1Qyw4REFBQzJCO29EQUFHM0IsV0FBVTs4REFBOEI7Ozs7Ozs4REFDNUMsOERBQUMyQjtvREFBRzNCLFdBQVU7OERBQWdDOzs7Ozs7Ozs7Ozs7c0RBRWhELDhEQUFDd0I7NENBQUd4QixXQUFVOzs4REFDWiw4REFBQzJCO29EQUFHM0IsV0FBVTs4REFDWiw0RUFBQ0s7d0RBQUtMLFdBQVU7a0VBQXVIOzs7Ozs7Ozs7Ozs4REFJekksOERBQUMyQjtvREFBRzNCLFdBQVU7OERBQThCOzs7Ozs7OERBQzVDLDhEQUFDMkI7b0RBQUczQixXQUFVOzhEQUE4Qjs7Ozs7OzhEQUM1Qyw4REFBQzJCO29EQUFHM0IsV0FBVTs4REFBZ0M7Ozs7Ozs7Ozs7OztzREFFaEQsOERBQUN3Qjs0Q0FBR3hCLFdBQVU7OzhEQUNaLDhEQUFDMkI7b0RBQUczQixXQUFVOzhEQUNaLDRFQUFDSzt3REFBS0wsV0FBVTtrRUFBZ0k7Ozs7Ozs7Ozs7OzhEQUlsSiw4REFBQzJCO29EQUFHM0IsV0FBVTs4REFBOEI7Ozs7Ozs4REFDNUMsOERBQUMyQjtvREFBRzNCLFdBQVU7OERBQThCOzs7Ozs7OERBQzVDLDhEQUFDMkI7b0RBQUczQixXQUFVOzhEQUFnQzs7Ozs7Ozs7Ozs7O3NEQUVoRCw4REFBQ3dCOzs4REFDQyw4REFBQ0c7b0RBQUczQixXQUFVOzhEQUNaLDRFQUFDSzt3REFBS0wsV0FBVTtrRUFBMEg7Ozs7Ozs7Ozs7OzhEQUk1SSw4REFBQzJCO29EQUFHM0IsV0FBVTs4REFBOEI7Ozs7Ozs4REFDNUMsOERBQUMyQjtvREFBRzNCLFdBQVU7OERBQThCOzs7Ozs7OERBQzVDLDhEQUFDMkI7b0RBQUczQixXQUFVOzhEQUFnQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFROUQ7S0EzTXdCRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9sZW5kaW5nL1JlYWxVc2VjYXNlLnRzeD82YTg1Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQge1xuICBVc2VycyxcbiAgVHJlbmRpbmdEb3duLFxuICBBbGVydFRyaWFuZ2xlLFxuICBEb2xsYXJTaWduLFxuICBCYXJDaGFydDMsXG4gIFNoaWVsZFxufSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgTWV0cmljQ2FyZCBmcm9tICdAL2NvbXBvbmVudHMvY2hhcnRzL01ldHJpY0NhcmQnXG5pbXBvcnQgUmlza0Rpc3RyaWJ1dGlvbkNoYXJ0IGZyb20gJ0AvY29tcG9uZW50cy9jaGFydHMvUmlza0Rpc3RyaWJ1dGlvbkNoYXJ0J1xuaW1wb3J0IENoYWluRGlzdHJpYnV0aW9uQ2hhcnQgZnJvbSAnQC9jb21wb25lbnRzL2NoYXJ0cy9DaGFpbkRpc3RyaWJ1dGlvbkNoYXJ0J1xuaW1wb3J0IExpcXVpZGF0aW9uVHJlbmRzQ2hhcnQgZnJvbSAnQC9jb21wb25lbnRzL2NoYXJ0cy9MaXF1aWRhdGlvblRyZW5kc0NoYXJ0J1xuaW1wb3J0IFRvcFdhbGxldHNUYWJsZSBmcm9tICdAL2NvbXBvbmVudHMvY2hhcnRzL1RvcFdhbGxldHNUYWJsZSdcbmltcG9ydCB7IGtleUZpbmRpbmdzIH0gZnJvbSAnQC9kYXRhL2xpcXVpZGF0aW9uRGF0YSdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGVuZGluZ1JlYWxVc2VjYXNlKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IHNtOnAtNiBzcGFjZS15LTQgc206c3BhY2UteS02IGJnLWJhY2tncm91bmQgbWluLWgtZnVsbFwiPlxuICAgICAgey8qIEhlYWRlciBTZWN0aW9uICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1zdXJmYWNlIGJvcmRlciBib3JkZXItYm9yZGVyLWNvbG9yIHJvdW5kZWQteGwgcC00IHNtOnAtNlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbGc6ZmxleC1yb3cgbGc6aXRlbXMtc3RhcnQgbGc6anVzdGlmeS1iZXR3ZWVuIHNwYWNlLXktNCBsZzpzcGFjZS15LTBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQteGwgc206dGV4dC0yeGwgZm9udC1ib2xkIHRleHQtdGV4dC1wcmltYXJ5IG1iLTJcIj5cbiAgICAgICAgICAgICAgRGVGaSBMZW5kaW5nIENyZWRpdHdvcnRoaW5lc3MgQW5hbHlzaXNcbiAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXRleHQtc2Vjb25kYXJ5IG1iLTQgdGV4dC1zbSBzbTp0ZXh0LWJhc2VcIj5cbiAgICAgICAgICAgICAgQ29tcHJlaGVuc2l2ZSBhbmFseXNpcyBvZiBEZUZpIHdhbGxldCBjcmVkaXR3b3J0aGluZXNzIHVzaW5nIHpTY29yZSBtZXRyaWNzLFxuICAgICAgICAgICAgICBiYXNlZCBvbiBhIHN0dWR5IG9mIHtrZXlGaW5kaW5ncy50b3RhbExpcXVpZGF0ZWRBZGRyZXNzZXMudG9Mb2NhbGVTdHJpbmcoKX0gbGlxdWlkYXRlZCB3YWxsZXQgYWRkcmVzc2VzXG4gICAgICAgICAgICAgIG9uIEFhdmUgb3ZlciB0aGUgbGFzdCA0OCBob3VycyAoMDItMDItMjAyNSB0byAwMy0wMi0yMDI1KS5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBzbTppdGVtcy1jZW50ZXIgc206c3BhY2UteC00IHNwYWNlLXktMiBzbTpzcGFjZS15LTAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1zZWNvbmRhcnkgcm91bmRlZC1mdWxsXCIgLz5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXRleHQtc2Vjb25kYXJ5XCI+TGl2ZSBEYXRhPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctcHJpbWFyeSByb3VuZGVkLWZ1bGxcIiAvPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtdGV4dC1zZWNvbmRhcnlcIj5SZWFsLXRpbWUgQW5hbHlzaXM8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tcHJpbWFyeS8xMCB0by1wcmltYXJ5LWxpZ2h0LzEwIGJvcmRlciBib3JkZXItcHJpbWFyeS8yMCByb3VuZGVkLWxnIHAtNCBsZzptbC00IGZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPFNoaWVsZCBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtcHJpbWFyeSBteC1hdXRvIG1iLTJcIiAvPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtdGV4dC1wcmltYXJ5XCI+Q292ZXJhZ2U8L3A+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXByaW1hcnlcIj57a2V5RmluZGluZ3MuY292ZXJhZ2VQZXJjZW50YWdlfSU8L3A+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC10ZXh0LXNlY29uZGFyeVwiPlxuICAgICAgICAgICAgICAgIHtrZXlGaW5kaW5ncy5hbmFseXplZFdhbGxldHMudG9Mb2NhbGVTdHJpbmcoKX0gd2FsbGV0cyBhbmFseXplZFxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEtleSBNZXRyaWNzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIHNtOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC00IHNtOmdhcC02XCI+XG4gICAgICAgIDxNZXRyaWNDYXJkXG4gICAgICAgICAgdGl0bGU9XCJUb3RhbCBXYWxsZXRzIFRyYWNrZWRcIlxuICAgICAgICAgIHZhbHVlPXtrZXlGaW5kaW5ncy50b3RhbExpcXVpZGF0ZWRBZGRyZXNzZXMudG9Mb2NhbGVTdHJpbmcoKX1cbiAgICAgICAgICBjaGFuZ2U9XCIrMi40JSB0aGlzIHdlZWtcIlxuICAgICAgICAgIGNoYW5nZVR5cGU9XCJwb3NpdGl2ZVwiXG4gICAgICAgICAgaWNvbj17VXNlcnN9XG4gICAgICAgICAgZGVzY3JpcHRpb249XCJMaXF1aWRhdGVkIGFkZHJlc3NlcyBhbmFseXplZFwiXG4gICAgICAgIC8+XG4gICAgICAgIDxNZXRyaWNDYXJkXG4gICAgICAgICAgdGl0bGU9XCJDaGFpbnMgSW50ZWdyYXRlZFwiXG4gICAgICAgICAgdmFsdWU9XCIxMFwiXG4gICAgICAgICAgY2hhbmdlPVwiKzIgbmV3IHRoaXMgbW9udGhcIlxuICAgICAgICAgIGNoYW5nZVR5cGU9XCJwb3NpdGl2ZVwiXG4gICAgICAgICAgaWNvbj17QmFyQ2hhcnQzfVxuICAgICAgICAgIGRlc2NyaXB0aW9uPVwiQmxvY2tjaGFpbiBuZXR3b3JrcyBjb3ZlcmVkXCJcbiAgICAgICAgLz5cbiAgICAgICAgPE1ldHJpY0NhcmRcbiAgICAgICAgICB0aXRsZT1cIlByb3RvY29scyBTdXBwb3J0ZWRcIlxuICAgICAgICAgIHZhbHVlPVwiMjUrXCJcbiAgICAgICAgICBjaGFuZ2U9XCJBYXZlLCBDb21wb3VuZCwgTWFrZXJEQU9cIlxuICAgICAgICAgIGNoYW5nZVR5cGU9XCJuZXV0cmFsXCJcbiAgICAgICAgICBpY29uPXtEb2xsYXJTaWdufVxuICAgICAgICAgIGRlc2NyaXB0aW9uPVwiRGVGaSBsZW5kaW5nIHByb3RvY29sc1wiXG4gICAgICAgIC8+XG4gICAgICAgIDxNZXRyaWNDYXJkXG4gICAgICAgICAgdGl0bGU9XCJEYWlseSBUcmFuc2FjdGlvbnNcIlxuICAgICAgICAgIHZhbHVlPVwiMS4yTVwiXG4gICAgICAgICAgY2hhbmdlPVwiKzEwLjclIGluY3JlYXNlXCJcbiAgICAgICAgICBjaGFuZ2VUeXBlPVwicG9zaXRpdmVcIlxuICAgICAgICAgIGljb249e1RyZW5kaW5nRG93bn1cbiAgICAgICAgICBkZXNjcmlwdGlvbj1cIlJlYWwtdGltZSBtb25pdG9yaW5nXCJcbiAgICAgICAgLz5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogUmVzZWFyY2ggT3ZlcnZpZXcgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXN1cmZhY2UgYm9yZGVyIGJvcmRlci1ib3JkZXItY29sb3Igcm91bmRlZC14bCBwLTQgc206cC02XCI+XG4gICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC10ZXh0LXByaW1hcnkgbWItNFwiPlJlc2VhcmNoIFF1ZXN0aW9uPC9oMj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tcHJpbWFyeS81IHRvLXByaW1hcnktbGlnaHQvNSBib3JkZXIgYm9yZGVyLXByaW1hcnkvMTAgcm91bmRlZC1sZyBwLTQgc206cC02XCI+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtYmFzZSBzbTp0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtdGV4dC1wcmltYXJ5IG1iLTNcIj5cbiAgICAgICAgICAgIENhbiBhIGRlY2VudHJhbGl6ZWQgcmVwdXRhdGlvbiBzeXN0ZW0gYnVpbHQgb24gRWlnZW5sYXllciBiZSBhIGdvb2QgYWxpYmkgZm9yIGNyZWRpdHdvcnRoaW5lc3M/XG4gICAgICAgICAgPC9oMz5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXRleHQtc2Vjb25kYXJ5IGxlYWRpbmctcmVsYXhlZCB0ZXh0LXNtIHNtOnRleHQtYmFzZVwiPlxuICAgICAgICAgICAgVGhpcyByZXNlYXJjaCBwcmVzZW50cyBhIGNvbXByZWhlbnNpdmUgYW5hbHlzaXMgb2YgRGVGaSB3YWxsZXQgY3JlZGl0d29ydGhpbmVzcyB1c2luZyB6U2NvcmUgbWV0cmljcy5cbiAgICAgICAgICAgIFRoZSBmaW5kaW5ncyBkZW1vbnN0cmF0ZSBob3cgelNjb3JlIGNhbiBzZXJ2ZSBhcyBhIHJlbGlhYmxlIHByZWRpY3RvciBmb3IgbGlxdWlkYXRpb24gcmlzayBhc3Nlc3NtZW50XG4gICAgICAgICAgICBpbiBEZUZpIGxlbmRpbmcsIHdpdGggdGhlIHByaW1hcnkgcmlzayB6b25lICgxOTAtMjAwKSBhY2NvdW50aW5nIGZvciA8c3Ryb25nIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeVwiPjI4Ljg3JTwvc3Ryb25nPiBvZlxuICAgICAgICAgICAgYWxsIGxpcXVpZGF0aW9ucy5cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDaGFydHMgR3JpZCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtNCBzbTpnYXAtNlwiPlxuICAgICAgICA8Umlza0Rpc3RyaWJ1dGlvbkNoYXJ0IC8+XG4gICAgICAgIDxDaGFpbkRpc3RyaWJ1dGlvbkNoYXJ0IC8+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGdhcC02XCI+XG4gICAgICAgIDxMaXF1aWRhdGlvblRyZW5kc0NoYXJ0IC8+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEtleSBGaW5kaW5ncyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc3VyZmFjZSBib3JkZXIgYm9yZGVyLWJvcmRlci1jb2xvciByb3VuZGVkLXhsIHAtNCBzbTpwLTZcIj5cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXRleHQtcHJpbWFyeSBtYi02XCI+S2V5IEZpbmRpbmdzPC9oMj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC00IHNtOmdhcC02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1zdXJmYWNlLWxpZ2h0IHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBtYi0zXCI+XG4gICAgICAgICAgICAgIDxBbGVydFRyaWFuZ2xlIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1kYW5nZXJcIiAvPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC10ZXh0LXByaW1hcnlcIj5QcmltYXJ5IFJpc2sgWm9uZTwvaDM+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWRhbmdlciBtYi0yXCI+MTkwLTIwMDwvcD5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC10ZXh0LXNlY29uZGFyeVwiPlxuICAgICAgICAgICAgICB7a2V5RmluZGluZ3MucHJpbWFyeVJpc2tab25lLnBlcmNlbnRhZ2V9JSBvZiBsaXF1aWRhdGlvbnMgKHtrZXlGaW5kaW5ncy5wcmltYXJ5Umlza1pvbmUuY291bnQudG9Mb2NhbGVTdHJpbmcoKX0gd2FsbGV0cylcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc3VyZmFjZS1saWdodCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgbWItM1wiPlxuICAgICAgICAgICAgICA8QmFyQ2hhcnQzIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1wcmltYXJ5XCIgLz5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtdGV4dC1wcmltYXJ5XCI+QXZlcmFnZSB6U2NvcmU8L2gzPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wcmltYXJ5IG1iLTJcIj57a2V5RmluZGluZ3MuYXZlcmFnZVpTY29yZX08L3A+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtdGV4dC1zZWNvbmRhcnlcIj5cbiAgICAgICAgICAgICAgTWVkaWFuOiB7a2V5RmluZGluZ3MubWVkaWFuWlNjb3JlfSAocmlnaHQtc2tld2VkIGRpc3RyaWJ1dGlvbilcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc3VyZmFjZS1saWdodCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgbWItM1wiPlxuICAgICAgICAgICAgICA8VHJlbmRpbmdEb3duIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1hY2NlbnRcIiAvPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC10ZXh0LXByaW1hcnlcIj5IaWdoIFJpc2sgVGhyZXNob2xkPC9oMz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtYWNjZW50IG1iLTJcIj4mbHQ7MjAwPC9wPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXRleHQtc2Vjb25kYXJ5XCI+XG4gICAgICAgICAgICAgIH40NSUgb2YgbGlxdWlkYXRpb25zIG9jY3VyIGluIHRoaXMgcmFuZ2VcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFRvcCBXYWxsZXRzIFRhYmxlICovfVxuICAgICAgPFRvcFdhbGxldHNUYWJsZSAvPlxuXG4gICAgICB7LyogUmlzayBBc3Nlc3NtZW50IEZyYW1ld29yayAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc3VyZmFjZSBib3JkZXIgYm9yZGVyLWJvcmRlci1jb2xvciByb3VuZGVkLXhsIHAtNCBzbTpwLTZcIj5cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXRleHQtcHJpbWFyeSBtYi02XCI+UmlzayBBc3Nlc3NtZW50IEZyYW1ld29yazwvaDI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgPHRhYmxlIGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgICAgICAgPHRoZWFkPlxuICAgICAgICAgICAgICA8dHIgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLWJvcmRlci1jb2xvclwiPlxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LWxlZnQgcHktMyBweC00IHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC10ZXh0LXNlY29uZGFyeVwiPlJpc2sgTGV2ZWw8L3RoPlxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LWxlZnQgcHktMyBweC00IHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC10ZXh0LXNlY29uZGFyeVwiPlNjb3JlIFJhbmdlPC90aD5cbiAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGV4dC1sZWZ0IHB5LTMgcHgtNCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtdGV4dC1zZWNvbmRhcnlcIj4lIG9mIExpcXVpZGF0aW9uczwvdGg+XG4gICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtbGVmdCBweS0zIHB4LTQgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXRleHQtc2Vjb25kYXJ5XCI+SW1wbGljYXRpb248L3RoPlxuICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgPC90aGVhZD5cbiAgICAgICAgICAgIDx0Ym9keT5cbiAgICAgICAgICAgICAgPHRyIGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1ib3JkZXItY29sb3IvNTBcIj5cbiAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00XCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLWRhbmdlci8xMCB0ZXh0LWRhbmdlciBib3JkZXIgYm9yZGVyLWRhbmdlci8yMFwiPlxuICAgICAgICAgICAgICAgICAgICBWZXJ5IEhpZ2hcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTQgdGV4dC10ZXh0LXByaW1hcnlcIj4mbHQ7MjAwPC90ZD5cbiAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00IHRleHQtdGV4dC1wcmltYXJ5XCI+fjQ1JTwvdGQ+XG4gICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNCB0ZXh0LXRleHQtc2Vjb25kYXJ5XCI+TmVlZHMgbWF4IGNvbGxhdGVyYWxpemF0aW9uPC90ZD5cbiAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgPHRyIGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1ib3JkZXItY29sb3IvNTBcIj5cbiAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00XCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLWFjY2VudC8xMCB0ZXh0LWFjY2VudCBib3JkZXIgYm9yZGVyLWFjY2VudC8yMFwiPlxuICAgICAgICAgICAgICAgICAgICBIaWdoXG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00IHRleHQtdGV4dC1wcmltYXJ5XCI+MjAw4oCTMzAwPC90ZD5cbiAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00IHRleHQtdGV4dC1wcmltYXJ5XCI+fjMwJTwvdGQ+XG4gICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNCB0ZXh0LXRleHQtc2Vjb25kYXJ5XCI+SGlnaGVyIGxpcXVpZGF0aW9uIHByb2JhYmlsaXR5PC90ZD5cbiAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgPHRyIGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1ib3JkZXItY29sb3IvNTBcIj5cbiAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00XCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLXNlY29uZGFyeS8xMCB0ZXh0LXNlY29uZGFyeSBib3JkZXIgYm9yZGVyLXNlY29uZGFyeS8yMFwiPlxuICAgICAgICAgICAgICAgICAgICBNb2RlcmF0ZVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNCB0ZXh0LXRleHQtcHJpbWFyeVwiPjMwMOKAkzQwMDwvdGQ+XG4gICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNCB0ZXh0LXRleHQtcHJpbWFyeVwiPn4yMCU8L3RkPlxuICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTQgdGV4dC10ZXh0LXNlY29uZGFyeVwiPlN0YW5kYXJkIGNvbGxhdGVyYWxpemF0aW9uPC90ZD5cbiAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgPHRyPlxuICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0zIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gYmctcHJpbWFyeS8xMCB0ZXh0LXByaW1hcnkgYm9yZGVyIGJvcmRlci1wcmltYXJ5LzIwXCI+XG4gICAgICAgICAgICAgICAgICAgIExvd2VyIFJpc2tcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTQgdGV4dC10ZXh0LXByaW1hcnlcIj40MDArPC90ZD5cbiAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00IHRleHQtdGV4dC1wcmltYXJ5XCI+fjUlPC90ZD5cbiAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00IHRleHQtdGV4dC1zZWNvbmRhcnlcIj5GYXZvcmFibGUgbGVuZGluZyB0ZXJtczwvdGQ+XG4gICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICA8L3Rib2R5PlxuICAgICAgICAgIDwvdGFibGU+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJVc2VycyIsIlRyZW5kaW5nRG93biIsIkFsZXJ0VHJpYW5nbGUiLCJEb2xsYXJTaWduIiwiQmFyQ2hhcnQzIiwiU2hpZWxkIiwiTWV0cmljQ2FyZCIsIlJpc2tEaXN0cmlidXRpb25DaGFydCIsIkNoYWluRGlzdHJpYnV0aW9uQ2hhcnQiLCJMaXF1aWRhdGlvblRyZW5kc0NoYXJ0IiwiVG9wV2FsbGV0c1RhYmxlIiwia2V5RmluZGluZ3MiLCJMZW5kaW5nUmVhbFVzZWNhc2UiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInAiLCJ0b3RhbExpcXVpZGF0ZWRBZGRyZXNzZXMiLCJ0b0xvY2FsZVN0cmluZyIsInNwYW4iLCJjb3ZlcmFnZVBlcmNlbnRhZ2UiLCJhbmFseXplZFdhbGxldHMiLCJ0aXRsZSIsInZhbHVlIiwiY2hhbmdlIiwiY2hhbmdlVHlwZSIsImljb24iLCJkZXNjcmlwdGlvbiIsImgyIiwiaDMiLCJzdHJvbmciLCJwcmltYXJ5Umlza1pvbmUiLCJwZXJjZW50YWdlIiwiY291bnQiLCJhdmVyYWdlWlNjb3JlIiwibWVkaWFuWlNjb3JlIiwidGFibGUiLCJ0aGVhZCIsInRyIiwidGgiLCJ0Ym9keSIsInRkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/lending/RealUsecase.tsx\n"));

/***/ })

});