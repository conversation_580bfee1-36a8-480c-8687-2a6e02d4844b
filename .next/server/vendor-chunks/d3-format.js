"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-format";
exports.ids = ["vendor-chunks/d3-format"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-format/src/defaultLocale.js":
/*!*****************************************************!*\
  !*** ./node_modules/d3-format/src/defaultLocale.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defaultLocale),\n/* harmony export */   format: () => (/* binding */ format),\n/* harmony export */   formatPrefix: () => (/* binding */ formatPrefix)\n/* harmony export */ });\n/* harmony import */ var _locale_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./locale.js */ \"(ssr)/./node_modules/d3-format/src/locale.js\");\n\nvar locale;\nvar format;\nvar formatPrefix;\ndefaultLocale({\n    thousands: \",\",\n    grouping: [\n        3\n    ],\n    currency: [\n        \"$\",\n        \"\"\n    ]\n});\nfunction defaultLocale(definition) {\n    locale = (0,_locale_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(definition);\n    format = locale.format;\n    formatPrefix = locale.formatPrefix;\n    return locale;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9kZWZhdWx0TG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBdUM7QUFFdkMsSUFBSUM7QUFDRyxJQUFJQyxPQUFPO0FBQ1gsSUFBSUMsYUFBYTtBQUV4QkMsY0FBYztJQUNaQyxXQUFXO0lBQ1hDLFVBQVU7UUFBQztLQUFFO0lBQ2JDLFVBQVU7UUFBQztRQUFLO0tBQUc7QUFDckI7QUFFZSxTQUFTSCxjQUFjSSxVQUFVO0lBQzlDUCxTQUFTRCxzREFBWUEsQ0FBQ1E7SUFDdEJOLFNBQVNELE9BQU9DLE1BQU07SUFDdEJDLGVBQWVGLE9BQU9FLFlBQVk7SUFDbEMsT0FBT0Y7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3pzY29yZS1kYXNoYm9hcmQvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9kZWZhdWx0TG9jYWxlLmpzP2Y4YWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGZvcm1hdExvY2FsZSBmcm9tIFwiLi9sb2NhbGUuanNcIjtcblxudmFyIGxvY2FsZTtcbmV4cG9ydCB2YXIgZm9ybWF0O1xuZXhwb3J0IHZhciBmb3JtYXRQcmVmaXg7XG5cbmRlZmF1bHRMb2NhbGUoe1xuICB0aG91c2FuZHM6IFwiLFwiLFxuICBncm91cGluZzogWzNdLFxuICBjdXJyZW5jeTogW1wiJFwiLCBcIlwiXVxufSk7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGRlZmF1bHRMb2NhbGUoZGVmaW5pdGlvbikge1xuICBsb2NhbGUgPSBmb3JtYXRMb2NhbGUoZGVmaW5pdGlvbik7XG4gIGZvcm1hdCA9IGxvY2FsZS5mb3JtYXQ7XG4gIGZvcm1hdFByZWZpeCA9IGxvY2FsZS5mb3JtYXRQcmVmaXg7XG4gIHJldHVybiBsb2NhbGU7XG59XG4iXSwibmFtZXMiOlsiZm9ybWF0TG9jYWxlIiwibG9jYWxlIiwiZm9ybWF0IiwiZm9ybWF0UHJlZml4IiwiZGVmYXVsdExvY2FsZSIsInRob3VzYW5kcyIsImdyb3VwaW5nIiwiY3VycmVuY3kiLCJkZWZpbml0aW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/defaultLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/exponent.js":
/*!************************************************!*\
  !*** ./node_modules/d3-format/src/exponent.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDecimal.js */ \"(ssr)/./node_modules/d3-format/src/formatDecimal.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x) {\n    return x = (0,_formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__.formatDecimalParts)(Math.abs(x)), x ? x[1] : NaN;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9leHBvbmVudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzRDtBQUV0RCw2QkFBZSxvQ0FBU0MsQ0FBQztJQUN2QixPQUFPQSxJQUFJRCxxRUFBa0JBLENBQUNFLEtBQUtDLEdBQUcsQ0FBQ0YsS0FBS0EsSUFBSUEsQ0FBQyxDQUFDLEVBQUUsR0FBR0c7QUFDekQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96c2NvcmUtZGFzaGJvYXJkLy4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZXhwb25lbnQuanM/NjQyNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2Zvcm1hdERlY2ltYWxQYXJ0c30gZnJvbSBcIi4vZm9ybWF0RGVjaW1hbC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbih4KSB7XG4gIHJldHVybiB4ID0gZm9ybWF0RGVjaW1hbFBhcnRzKE1hdGguYWJzKHgpKSwgeCA/IHhbMV0gOiBOYU47XG59XG4iXSwibmFtZXMiOlsiZm9ybWF0RGVjaW1hbFBhcnRzIiwieCIsIk1hdGgiLCJhYnMiLCJOYU4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/exponent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/formatDecimal.js":
/*!*****************************************************!*\
  !*** ./node_modules/d3-format/src/formatDecimal.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   formatDecimalParts: () => (/* binding */ formatDecimalParts)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x) {\n    return Math.abs(x = Math.round(x)) >= 1e21 ? x.toLocaleString(\"en\").replace(/,/g, \"\") : x.toString(10);\n}\n// Computes the decimal coefficient and exponent of the specified number x with\n// significant digits p, where x is positive and p is in [1, 21] or undefined.\n// For example, formatDecimalParts(1.23) returns [\"123\", 0].\nfunction formatDecimalParts(x, p) {\n    if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf(\"e\")) < 0) return null; // NaN, ±Infinity\n    var i, coefficient = x.slice(0, i);\n    // The string returned by toExponential either has the form \\d\\.\\d+e[-+]\\d+\n    // (e.g., 1.2e+3) or the form \\de[-+]\\d+ (e.g., 1e+3).\n    return [\n        coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,\n        +x.slice(i + 1)\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/formatDecimal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/formatGroup.js":
/*!***************************************************!*\
  !*** ./node_modules/d3-format/src/formatGroup.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(grouping, thousands) {\n    return function(value, width) {\n        var i = value.length, t = [], j = 0, g = grouping[0], length = 0;\n        while(i > 0 && g > 0){\n            if (length + g + 1 > width) g = Math.max(1, width - length);\n            t.push(value.substring(i -= g, i + g));\n            if ((length += g + 1) > width) break;\n            g = grouping[j = (j + 1) % grouping.length];\n        }\n        return t.reverse().join(thousands);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9mb3JtYXRHcm91cC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVNBLFFBQVEsRUFBRUMsU0FBUztJQUN6QyxPQUFPLFNBQVNDLEtBQUssRUFBRUMsS0FBSztRQUMxQixJQUFJQyxJQUFJRixNQUFNRyxNQUFNLEVBQ2hCQyxJQUFJLEVBQUUsRUFDTkMsSUFBSSxHQUNKQyxJQUFJUixRQUFRLENBQUMsRUFBRSxFQUNmSyxTQUFTO1FBRWIsTUFBT0QsSUFBSSxLQUFLSSxJQUFJLEVBQUc7WUFDckIsSUFBSUgsU0FBU0csSUFBSSxJQUFJTCxPQUFPSyxJQUFJQyxLQUFLQyxHQUFHLENBQUMsR0FBR1AsUUFBUUU7WUFDcERDLEVBQUVLLElBQUksQ0FBQ1QsTUFBTVUsU0FBUyxDQUFDUixLQUFLSSxHQUFHSixJQUFJSTtZQUNuQyxJQUFJLENBQUNILFVBQVVHLElBQUksS0FBS0wsT0FBTztZQUMvQkssSUFBSVIsUUFBUSxDQUFDTyxJQUFJLENBQUNBLElBQUksS0FBS1AsU0FBU0ssTUFBTSxDQUFDO1FBQzdDO1FBRUEsT0FBT0MsRUFBRU8sT0FBTyxHQUFHQyxJQUFJLENBQUNiO0lBQzFCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96c2NvcmUtZGFzaGJvYXJkLy4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZm9ybWF0R3JvdXAuanM/ZmQ2OCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbihncm91cGluZywgdGhvdXNhbmRzKSB7XG4gIHJldHVybiBmdW5jdGlvbih2YWx1ZSwgd2lkdGgpIHtcbiAgICB2YXIgaSA9IHZhbHVlLmxlbmd0aCxcbiAgICAgICAgdCA9IFtdLFxuICAgICAgICBqID0gMCxcbiAgICAgICAgZyA9IGdyb3VwaW5nWzBdLFxuICAgICAgICBsZW5ndGggPSAwO1xuXG4gICAgd2hpbGUgKGkgPiAwICYmIGcgPiAwKSB7XG4gICAgICBpZiAobGVuZ3RoICsgZyArIDEgPiB3aWR0aCkgZyA9IE1hdGgubWF4KDEsIHdpZHRoIC0gbGVuZ3RoKTtcbiAgICAgIHQucHVzaCh2YWx1ZS5zdWJzdHJpbmcoaSAtPSBnLCBpICsgZykpO1xuICAgICAgaWYgKChsZW5ndGggKz0gZyArIDEpID4gd2lkdGgpIGJyZWFrO1xuICAgICAgZyA9IGdyb3VwaW5nW2ogPSAoaiArIDEpICUgZ3JvdXBpbmcubGVuZ3RoXTtcbiAgICB9XG5cbiAgICByZXR1cm4gdC5yZXZlcnNlKCkuam9pbih0aG91c2FuZHMpO1xuICB9O1xufVxuIl0sIm5hbWVzIjpbImdyb3VwaW5nIiwidGhvdXNhbmRzIiwidmFsdWUiLCJ3aWR0aCIsImkiLCJsZW5ndGgiLCJ0IiwiaiIsImciLCJNYXRoIiwibWF4IiwicHVzaCIsInN1YnN0cmluZyIsInJldmVyc2UiLCJqb2luIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/formatGroup.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/formatNumerals.js":
/*!******************************************************!*\
  !*** ./node_modules/d3-format/src/formatNumerals.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(numerals) {\n    return function(value) {\n        return value.replace(/[0-9]/g, function(i) {\n            return numerals[+i];\n        });\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9mb3JtYXROdW1lcmFscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVNBLFFBQVE7SUFDOUIsT0FBTyxTQUFTQyxLQUFLO1FBQ25CLE9BQU9BLE1BQU1DLE9BQU8sQ0FBQyxVQUFVLFNBQVNDLENBQUM7WUFDdkMsT0FBT0gsUUFBUSxDQUFDLENBQUNHLEVBQUU7UUFDckI7SUFDRjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venNjb3JlLWRhc2hib2FyZC8uL25vZGVfbW9kdWxlcy9kMy1mb3JtYXQvc3JjL2Zvcm1hdE51bWVyYWxzLmpzP2U1MTMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24obnVtZXJhbHMpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uKHZhbHVlKSB7XG4gICAgcmV0dXJuIHZhbHVlLnJlcGxhY2UoL1swLTldL2csIGZ1bmN0aW9uKGkpIHtcbiAgICAgIHJldHVybiBudW1lcmFsc1sraV07XG4gICAgfSk7XG4gIH07XG59XG4iXSwibmFtZXMiOlsibnVtZXJhbHMiLCJ2YWx1ZSIsInJlcGxhY2UiLCJpIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/formatNumerals.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/formatPrefixAuto.js":
/*!********************************************************!*\
  !*** ./node_modules/d3-format/src/formatPrefixAuto.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prefixExponent: () => (/* binding */ prefixExponent)\n/* harmony export */ });\n/* harmony import */ var _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDecimal.js */ \"(ssr)/./node_modules/d3-format/src/formatDecimal.js\");\n\nvar prefixExponent;\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x, p) {\n    var d = (0,_formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__.formatDecimalParts)(x, p);\n    if (!d) return x + \"\";\n    var coefficient = d[0], exponent = d[1], i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1, n = coefficient.length;\n    return i === n ? coefficient : i > n ? coefficient + new Array(i - n + 1).join(\"0\") : i > 0 ? coefficient.slice(0, i) + \".\" + coefficient.slice(i) : \"0.\" + new Array(1 - i).join(\"0\") + (0,_formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__.formatDecimalParts)(x, Math.max(0, p + i - 1))[0]; // less than 1y!\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/formatPrefixAuto.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/formatRounded.js":
/*!*****************************************************!*\
  !*** ./node_modules/d3-format/src/formatRounded.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDecimal.js */ \"(ssr)/./node_modules/d3-format/src/formatDecimal.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x, p) {\n    var d = (0,_formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__.formatDecimalParts)(x, p);\n    if (!d) return x + \"\";\n    var coefficient = d[0], exponent = d[1];\n    return exponent < 0 ? \"0.\" + new Array(-exponent).join(\"0\") + coefficient : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + \".\" + coefficient.slice(exponent + 1) : coefficient + new Array(exponent - coefficient.length + 2).join(\"0\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9mb3JtYXRSb3VuZGVkLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNEO0FBRXRELDZCQUFlLG9DQUFTQyxDQUFDLEVBQUVDLENBQUM7SUFDMUIsSUFBSUMsSUFBSUgscUVBQWtCQSxDQUFDQyxHQUFHQztJQUM5QixJQUFJLENBQUNDLEdBQUcsT0FBT0YsSUFBSTtJQUNuQixJQUFJRyxjQUFjRCxDQUFDLENBQUMsRUFBRSxFQUNsQkUsV0FBV0YsQ0FBQyxDQUFDLEVBQUU7SUFDbkIsT0FBT0UsV0FBVyxJQUFJLE9BQU8sSUFBSUMsTUFBTSxDQUFDRCxVQUFVRSxJQUFJLENBQUMsT0FBT0gsY0FDeERBLFlBQVlJLE1BQU0sR0FBR0gsV0FBVyxJQUFJRCxZQUFZSyxLQUFLLENBQUMsR0FBR0osV0FBVyxLQUFLLE1BQU1ELFlBQVlLLEtBQUssQ0FBQ0osV0FBVyxLQUM1R0QsY0FBYyxJQUFJRSxNQUFNRCxXQUFXRCxZQUFZSSxNQUFNLEdBQUcsR0FBR0QsSUFBSSxDQUFDO0FBQ3hFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venNjb3JlLWRhc2hib2FyZC8uL25vZGVfbW9kdWxlcy9kMy1mb3JtYXQvc3JjL2Zvcm1hdFJvdW5kZWQuanM/ODg5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2Zvcm1hdERlY2ltYWxQYXJ0c30gZnJvbSBcIi4vZm9ybWF0RGVjaW1hbC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbih4LCBwKSB7XG4gIHZhciBkID0gZm9ybWF0RGVjaW1hbFBhcnRzKHgsIHApO1xuICBpZiAoIWQpIHJldHVybiB4ICsgXCJcIjtcbiAgdmFyIGNvZWZmaWNpZW50ID0gZFswXSxcbiAgICAgIGV4cG9uZW50ID0gZFsxXTtcbiAgcmV0dXJuIGV4cG9uZW50IDwgMCA/IFwiMC5cIiArIG5ldyBBcnJheSgtZXhwb25lbnQpLmpvaW4oXCIwXCIpICsgY29lZmZpY2llbnRcbiAgICAgIDogY29lZmZpY2llbnQubGVuZ3RoID4gZXhwb25lbnQgKyAxID8gY29lZmZpY2llbnQuc2xpY2UoMCwgZXhwb25lbnQgKyAxKSArIFwiLlwiICsgY29lZmZpY2llbnQuc2xpY2UoZXhwb25lbnQgKyAxKVxuICAgICAgOiBjb2VmZmljaWVudCArIG5ldyBBcnJheShleHBvbmVudCAtIGNvZWZmaWNpZW50Lmxlbmd0aCArIDIpLmpvaW4oXCIwXCIpO1xufVxuIl0sIm5hbWVzIjpbImZvcm1hdERlY2ltYWxQYXJ0cyIsIngiLCJwIiwiZCIsImNvZWZmaWNpZW50IiwiZXhwb25lbnQiLCJBcnJheSIsImpvaW4iLCJsZW5ndGgiLCJzbGljZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/formatRounded.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/formatSpecifier.js":
/*!*******************************************************!*\
  !*** ./node_modules/d3-format/src/formatSpecifier.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FormatSpecifier: () => (/* binding */ FormatSpecifier),\n/* harmony export */   \"default\": () => (/* binding */ formatSpecifier)\n/* harmony export */ });\n// [[fill]align][sign][symbol][0][width][,][.precision][~][type]\nvar re = /^(?:(.)?([<>=^]))?([+\\-( ])?([$#])?(0)?(\\d+)?(,)?(\\.\\d+)?(~)?([a-z%])?$/i;\nfunction formatSpecifier(specifier) {\n    if (!(match = re.exec(specifier))) throw new Error(\"invalid format: \" + specifier);\n    var match;\n    return new FormatSpecifier({\n        fill: match[1],\n        align: match[2],\n        sign: match[3],\n        symbol: match[4],\n        zero: match[5],\n        width: match[6],\n        comma: match[7],\n        precision: match[8] && match[8].slice(1),\n        trim: match[9],\n        type: match[10]\n    });\n}\nformatSpecifier.prototype = FormatSpecifier.prototype; // instanceof\nfunction FormatSpecifier(specifier) {\n    this.fill = specifier.fill === undefined ? \" \" : specifier.fill + \"\";\n    this.align = specifier.align === undefined ? \">\" : specifier.align + \"\";\n    this.sign = specifier.sign === undefined ? \"-\" : specifier.sign + \"\";\n    this.symbol = specifier.symbol === undefined ? \"\" : specifier.symbol + \"\";\n    this.zero = !!specifier.zero;\n    this.width = specifier.width === undefined ? undefined : +specifier.width;\n    this.comma = !!specifier.comma;\n    this.precision = specifier.precision === undefined ? undefined : +specifier.precision;\n    this.trim = !!specifier.trim;\n    this.type = specifier.type === undefined ? \"\" : specifier.type + \"\";\n}\nFormatSpecifier.prototype.toString = function() {\n    return this.fill + this.align + this.sign + this.symbol + (this.zero ? \"0\" : \"\") + (this.width === undefined ? \"\" : Math.max(1, this.width | 0)) + (this.comma ? \",\" : \"\") + (this.precision === undefined ? \"\" : \".\" + Math.max(0, this.precision | 0)) + (this.trim ? \"~\" : \"\") + this.type;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/formatSpecifier.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/formatTrim.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-format/src/formatTrim.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(s) {\n    out: for(var n = s.length, i = 1, i0 = -1, i1; i < n; ++i){\n        switch(s[i]){\n            case \".\":\n                i0 = i1 = i;\n                break;\n            case \"0\":\n                if (i0 === 0) i0 = i;\n                i1 = i;\n                break;\n            default:\n                if (!+s[i]) break out;\n                if (i0 > 0) i0 = 0;\n                break;\n        }\n    }\n    return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9mb3JtYXRUcmltLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSwrREFBK0Q7QUFDL0QsNkJBQWUsb0NBQVNBLENBQUM7SUFDdkJDLEtBQUssSUFBSyxJQUFJQyxJQUFJRixFQUFFRyxNQUFNLEVBQUVDLElBQUksR0FBR0MsS0FBSyxDQUFDLEdBQUdDLElBQUlGLElBQUlGLEdBQUcsRUFBRUUsRUFBRztRQUMxRCxPQUFRSixDQUFDLENBQUNJLEVBQUU7WUFDVixLQUFLO2dCQUFLQyxLQUFLQyxLQUFLRjtnQkFBRztZQUN2QixLQUFLO2dCQUFLLElBQUlDLE9BQU8sR0FBR0EsS0FBS0Q7Z0JBQUdFLEtBQUtGO2dCQUFHO1lBQ3hDO2dCQUFTLElBQUksQ0FBQyxDQUFDSixDQUFDLENBQUNJLEVBQUUsRUFBRSxNQUFNSDtnQkFBSyxJQUFJSSxLQUFLLEdBQUdBLEtBQUs7Z0JBQUc7UUFDdEQ7SUFDRjtJQUNBLE9BQU9BLEtBQUssSUFBSUwsRUFBRU8sS0FBSyxDQUFDLEdBQUdGLE1BQU1MLEVBQUVPLEtBQUssQ0FBQ0QsS0FBSyxLQUFLTjtBQUNyRCIsInNvdXJjZXMiOlsid2VicGFjazovL3pzY29yZS1kYXNoYm9hcmQvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9mb3JtYXRUcmltLmpzP2UyZjEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gVHJpbXMgaW5zaWduaWZpY2FudCB6ZXJvcywgZS5nLiwgcmVwbGFjZXMgMS4yMDAwayB3aXRoIDEuMmsuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihzKSB7XG4gIG91dDogZm9yICh2YXIgbiA9IHMubGVuZ3RoLCBpID0gMSwgaTAgPSAtMSwgaTE7IGkgPCBuOyArK2kpIHtcbiAgICBzd2l0Y2ggKHNbaV0pIHtcbiAgICAgIGNhc2UgXCIuXCI6IGkwID0gaTEgPSBpOyBicmVhaztcbiAgICAgIGNhc2UgXCIwXCI6IGlmIChpMCA9PT0gMCkgaTAgPSBpOyBpMSA9IGk7IGJyZWFrO1xuICAgICAgZGVmYXVsdDogaWYgKCErc1tpXSkgYnJlYWsgb3V0OyBpZiAoaTAgPiAwKSBpMCA9IDA7IGJyZWFrO1xuICAgIH1cbiAgfVxuICByZXR1cm4gaTAgPiAwID8gcy5zbGljZSgwLCBpMCkgKyBzLnNsaWNlKGkxICsgMSkgOiBzO1xufVxuIl0sIm5hbWVzIjpbInMiLCJvdXQiLCJuIiwibGVuZ3RoIiwiaSIsImkwIiwiaTEiLCJzbGljZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/formatTrim.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/formatTypes.js":
/*!***************************************************!*\
  !*** ./node_modules/d3-format/src/formatTypes.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDecimal.js */ \"(ssr)/./node_modules/d3-format/src/formatDecimal.js\");\n/* harmony import */ var _formatPrefixAuto_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./formatPrefixAuto.js */ \"(ssr)/./node_modules/d3-format/src/formatPrefixAuto.js\");\n/* harmony import */ var _formatRounded_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./formatRounded.js */ \"(ssr)/./node_modules/d3-format/src/formatRounded.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    \"%\": (x, p)=>(x * 100).toFixed(p),\n    \"b\": (x)=>Math.round(x).toString(2),\n    \"c\": (x)=>x + \"\",\n    \"d\": _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    \"e\": (x, p)=>x.toExponential(p),\n    \"f\": (x, p)=>x.toFixed(p),\n    \"g\": (x, p)=>x.toPrecision(p),\n    \"o\": (x)=>Math.round(x).toString(8),\n    \"p\": (x, p)=>(0,_formatRounded_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(x * 100, p),\n    \"r\": _formatRounded_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    \"s\": _formatPrefixAuto_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    \"X\": (x)=>Math.round(x).toString(16).toUpperCase(),\n    \"x\": (x)=>Math.round(x).toString(16)\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/formatTypes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/identity.js":
/*!************************************************!*\
  !*** ./node_modules/d3-format/src/identity.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x) {\n    return x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9pZGVudGl0eS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVNBLENBQUM7SUFDdkIsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3pzY29yZS1kYXNoYm9hcmQvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9pZGVudGl0eS5qcz85YmJlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHgpIHtcbiAgcmV0dXJuIHg7XG59XG4iXSwibmFtZXMiOlsieCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/identity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/locale.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-format/src/locale.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _exponent_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./exponent.js */ \"(ssr)/./node_modules/d3-format/src/exponent.js\");\n/* harmony import */ var _formatGroup_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./formatGroup.js */ \"(ssr)/./node_modules/d3-format/src/formatGroup.js\");\n/* harmony import */ var _formatNumerals_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./formatNumerals.js */ \"(ssr)/./node_modules/d3-format/src/formatNumerals.js\");\n/* harmony import */ var _formatSpecifier_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./formatSpecifier.js */ \"(ssr)/./node_modules/d3-format/src/formatSpecifier.js\");\n/* harmony import */ var _formatTrim_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./formatTrim.js */ \"(ssr)/./node_modules/d3-format/src/formatTrim.js\");\n/* harmony import */ var _formatTypes_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./formatTypes.js */ \"(ssr)/./node_modules/d3-format/src/formatTypes.js\");\n/* harmony import */ var _formatPrefixAuto_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./formatPrefixAuto.js */ \"(ssr)/./node_modules/d3-format/src/formatPrefixAuto.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./identity.js */ \"(ssr)/./node_modules/d3-format/src/identity.js\");\n\n\n\n\n\n\n\n\nvar map = Array.prototype.map, prefixes = [\n    \"y\",\n    \"z\",\n    \"a\",\n    \"f\",\n    \"p\",\n    \"n\",\n    \"\\xb5\",\n    \"m\",\n    \"\",\n    \"k\",\n    \"M\",\n    \"G\",\n    \"T\",\n    \"P\",\n    \"E\",\n    \"Z\",\n    \"Y\"\n];\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(locale) {\n    var group = locale.grouping === undefined || locale.thousands === undefined ? _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : (0,_formatGroup_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(map.call(locale.grouping, Number), locale.thousands + \"\"), currencyPrefix = locale.currency === undefined ? \"\" : locale.currency[0] + \"\", currencySuffix = locale.currency === undefined ? \"\" : locale.currency[1] + \"\", decimal = locale.decimal === undefined ? \".\" : locale.decimal + \"\", numerals = locale.numerals === undefined ? _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : (0,_formatNumerals_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(map.call(locale.numerals, String)), percent = locale.percent === undefined ? \"%\" : locale.percent + \"\", minus = locale.minus === undefined ? \"−\" : locale.minus + \"\", nan = locale.nan === undefined ? \"NaN\" : locale.nan + \"\";\n    function newFormat(specifier) {\n        specifier = (0,_formatSpecifier_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(specifier);\n        var fill = specifier.fill, align = specifier.align, sign = specifier.sign, symbol = specifier.symbol, zero = specifier.zero, width = specifier.width, comma = specifier.comma, precision = specifier.precision, trim = specifier.trim, type = specifier.type;\n        // The \"n\" type is an alias for \",g\".\n        if (type === \"n\") comma = true, type = \"g\";\n        else if (!_formatTypes_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"][type]) precision === undefined && (precision = 12), trim = true, type = \"g\";\n        // If zero fill is specified, padding goes after sign and before digits.\n        if (zero || fill === \"0\" && align === \"=\") zero = true, fill = \"0\", align = \"=\";\n        // Compute the prefix and suffix.\n        // For SI-prefix, the suffix is lazily computed.\n        var prefix = symbol === \"$\" ? currencyPrefix : symbol === \"#\" && /[boxX]/.test(type) ? \"0\" + type.toLowerCase() : \"\", suffix = symbol === \"$\" ? currencySuffix : /[%p]/.test(type) ? percent : \"\";\n        // What format function should we use?\n        // Is this an integer type?\n        // Can this type generate exponential notation?\n        var formatType = _formatTypes_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"][type], maybeSuffix = /[defgprs%]/.test(type);\n        // Set the default precision if not specified,\n        // or clamp the specified precision to the supported range.\n        // For significant precision, it must be in [1, 21].\n        // For fixed precision, it must be in [0, 20].\n        precision = precision === undefined ? 6 : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision)) : Math.max(0, Math.min(20, precision));\n        function format(value) {\n            var valuePrefix = prefix, valueSuffix = suffix, i, n, c;\n            if (type === \"c\") {\n                valueSuffix = formatType(value) + valueSuffix;\n                value = \"\";\n            } else {\n                value = +value;\n                // Determine the sign. -0 is not less than 0, but 1 / -0 is!\n                var valueNegative = value < 0 || 1 / value < 0;\n                // Perform the initial formatting.\n                value = isNaN(value) ? nan : formatType(Math.abs(value), precision);\n                // Trim insignificant zeros.\n                if (trim) value = (0,_formatTrim_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(value);\n                // If a negative value rounds to zero after formatting, and no explicit positive sign is requested, hide the sign.\n                if (valueNegative && +value === 0 && sign !== \"+\") valueNegative = false;\n                // Compute the prefix and suffix.\n                valuePrefix = (valueNegative ? sign === \"(\" ? sign : minus : sign === \"-\" || sign === \"(\" ? \"\" : sign) + valuePrefix;\n                valueSuffix = (type === \"s\" ? prefixes[8 + _formatPrefixAuto_js__WEBPACK_IMPORTED_MODULE_6__.prefixExponent / 3] : \"\") + valueSuffix + (valueNegative && sign === \"(\" ? \")\" : \"\");\n                // Break the formatted value into the integer “value” part that can be\n                // grouped, and fractional or exponential “suffix” part that is not.\n                if (maybeSuffix) {\n                    i = -1, n = value.length;\n                    while(++i < n){\n                        if (c = value.charCodeAt(i), 48 > c || c > 57) {\n                            valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;\n                            value = value.slice(0, i);\n                            break;\n                        }\n                    }\n                }\n            }\n            // If the fill character is not \"0\", grouping is applied before padding.\n            if (comma && !zero) value = group(value, Infinity);\n            // Compute the padding.\n            var length = valuePrefix.length + value.length + valueSuffix.length, padding = length < width ? new Array(width - length + 1).join(fill) : \"\";\n            // If the fill character is \"0\", grouping is applied after padding.\n            if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = \"\";\n            // Reconstruct the final output based on the desired alignment.\n            switch(align){\n                case \"<\":\n                    value = valuePrefix + value + valueSuffix + padding;\n                    break;\n                case \"=\":\n                    value = valuePrefix + padding + value + valueSuffix;\n                    break;\n                case \"^\":\n                    value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length);\n                    break;\n                default:\n                    value = padding + valuePrefix + value + valueSuffix;\n                    break;\n            }\n            return numerals(value);\n        }\n        format.toString = function() {\n            return specifier + \"\";\n        };\n        return format;\n    }\n    function formatPrefix(specifier, value) {\n        var f = newFormat((specifier = (0,_formatSpecifier_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(specifier), specifier.type = \"f\", specifier)), e = Math.max(-8, Math.min(8, Math.floor((0,_exponent_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(value) / 3))) * 3, k = Math.pow(10, -e), prefix = prefixes[8 + e / 3];\n        return function(value) {\n            return f(k * value) + prefix;\n        };\n    }\n    return {\n        format: newFormat,\n        formatPrefix: formatPrefix\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/locale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/precisionFixed.js":
/*!******************************************************!*\
  !*** ./node_modules/d3-format/src/precisionFixed.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _exponent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./exponent.js */ \"(ssr)/./node_modules/d3-format/src/exponent.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(step) {\n    return Math.max(0, -(0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Math.abs(step)));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9wcmVjaXNpb25GaXhlZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxQztBQUVyQyw2QkFBZSxvQ0FBU0MsSUFBSTtJQUMxQixPQUFPQyxLQUFLQyxHQUFHLENBQUMsR0FBRyxDQUFDSCx3REFBUUEsQ0FBQ0UsS0FBS0UsR0FBRyxDQUFDSDtBQUN4QyIsInNvdXJjZXMiOlsid2VicGFjazovL3pzY29yZS1kYXNoYm9hcmQvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9wcmVjaXNpb25GaXhlZC5qcz84Mzg3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBleHBvbmVudCBmcm9tIFwiLi9leHBvbmVudC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihzdGVwKSB7XG4gIHJldHVybiBNYXRoLm1heCgwLCAtZXhwb25lbnQoTWF0aC5hYnMoc3RlcCkpKTtcbn1cbiJdLCJuYW1lcyI6WyJleHBvbmVudCIsInN0ZXAiLCJNYXRoIiwibWF4IiwiYWJzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/precisionFixed.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/precisionPrefix.js":
/*!*******************************************************!*\
  !*** ./node_modules/d3-format/src/precisionPrefix.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _exponent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./exponent.js */ \"(ssr)/./node_modules/d3-format/src/exponent.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(step, value) {\n    return Math.max(0, Math.max(-8, Math.min(8, Math.floor((0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value) / 3))) * 3 - (0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Math.abs(step)));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9wcmVjaXNpb25QcmVmaXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUM7QUFFckMsNkJBQWUsb0NBQVNDLElBQUksRUFBRUMsS0FBSztJQUNqQyxPQUFPQyxLQUFLQyxHQUFHLENBQUMsR0FBR0QsS0FBS0MsR0FBRyxDQUFDLENBQUMsR0FBR0QsS0FBS0UsR0FBRyxDQUFDLEdBQUdGLEtBQUtHLEtBQUssQ0FBQ04sd0RBQVFBLENBQUNFLFNBQVMsT0FBTyxJQUFJRix3REFBUUEsQ0FBQ0csS0FBS0ksR0FBRyxDQUFDTjtBQUN4RyIsInNvdXJjZXMiOlsid2VicGFjazovL3pzY29yZS1kYXNoYm9hcmQvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9wcmVjaXNpb25QcmVmaXguanM/MzJjYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZXhwb25lbnQgZnJvbSBcIi4vZXhwb25lbnQuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oc3RlcCwgdmFsdWUpIHtcbiAgcmV0dXJuIE1hdGgubWF4KDAsIE1hdGgubWF4KC04LCBNYXRoLm1pbig4LCBNYXRoLmZsb29yKGV4cG9uZW50KHZhbHVlKSAvIDMpKSkgKiAzIC0gZXhwb25lbnQoTWF0aC5hYnMoc3RlcCkpKTtcbn1cbiJdLCJuYW1lcyI6WyJleHBvbmVudCIsInN0ZXAiLCJ2YWx1ZSIsIk1hdGgiLCJtYXgiLCJtaW4iLCJmbG9vciIsImFicyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/precisionPrefix.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/precisionRound.js":
/*!******************************************************!*\
  !*** ./node_modules/d3-format/src/precisionRound.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _exponent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./exponent.js */ \"(ssr)/./node_modules/d3-format/src/exponent.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(step, max) {\n    step = Math.abs(step), max = Math.abs(max) - step;\n    return Math.max(0, (0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(max) - (0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(step)) + 1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9wcmVjaXNpb25Sb3VuZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxQztBQUVyQyw2QkFBZSxvQ0FBU0MsSUFBSSxFQUFFQyxHQUFHO0lBQy9CRCxPQUFPRSxLQUFLQyxHQUFHLENBQUNILE9BQU9DLE1BQU1DLEtBQUtDLEdBQUcsQ0FBQ0YsT0FBT0Q7SUFDN0MsT0FBT0UsS0FBS0QsR0FBRyxDQUFDLEdBQUdGLHdEQUFRQSxDQUFDRSxPQUFPRix3REFBUUEsQ0FBQ0MsU0FBUztBQUN2RCIsInNvdXJjZXMiOlsid2VicGFjazovL3pzY29yZS1kYXNoYm9hcmQvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9wcmVjaXNpb25Sb3VuZC5qcz8yMTFhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBleHBvbmVudCBmcm9tIFwiLi9leHBvbmVudC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihzdGVwLCBtYXgpIHtcbiAgc3RlcCA9IE1hdGguYWJzKHN0ZXApLCBtYXggPSBNYXRoLmFicyhtYXgpIC0gc3RlcDtcbiAgcmV0dXJuIE1hdGgubWF4KDAsIGV4cG9uZW50KG1heCkgLSBleHBvbmVudChzdGVwKSkgKyAxO1xufVxuIl0sIm5hbWVzIjpbImV4cG9uZW50Iiwic3RlcCIsIm1heCIsIk1hdGgiLCJhYnMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/precisionRound.js\n");

/***/ })

};
;